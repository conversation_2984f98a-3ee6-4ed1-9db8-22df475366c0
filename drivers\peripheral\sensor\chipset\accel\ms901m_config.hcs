#include "../sensor_common.hcs"
root {
    accel_ms901m_chip_config : sensorConfig {
        match_attr = "hdf_sensor_accel_ms901m_driver";
        sensorInfo :: sensorDeviceInfo {
            sensorName = "accelerometer";
            vendorName = "ms901m_accel"; // max string length is 16 bytes
            sensorTypeId = 1; // enum SensorTypeTag
            sensorId = 1; // user define sensor id
            power = 100;
            minDelay = 50000000; // nanosecond
            maxDelay = 1000000000; // nanosecond
        }
        sensorBusConfig :: sensorBusInfo {
            busType = 3; // 3:uart
            busNum = 5;  // uart5
            busAddr = 0;
            regWidth = 1; // 1byte
        }
        sensorIdAttr :: sensorIdInfo {
            chipName = "ms901m";
            chipIdRegister = 0x03; // GYRO frame ID
            chipIdValue = 0x03;
        }

        sensorRegConfig :: sensorRegInfo {
            /*  regAddr: register address
                regValue: config register value
                regMask: config register mask
                regLen: number of value
                regDelay: config register delay time (ms)
                regOpsType: enum SensorOpsType 0-none 1-read 2-write 3-read_check 4-update_bit
                regCalType: enum SensorBitCalType 0-none 1-set 2-revert 3-xor 4-left_shift 5-right_shift
                regShiftNum: shift bits
                regDebug: 0-no debug 1-debug
                regSave: 0-no save 1-save
            */
            /* Group 1 reg config */
            groupLen = 1;
            regAddr = [0x03];
            regValue = [0x03];
            regMask = [0xFF];
            regLen = [1];
            regDelay = [0];
            regOpsType = [0];
            regCalType = [0];
            regShiftNum = [0];
            regDebug = [0];
            regSave = [0];
        }
    }
}
