/*
 * Copyright (c) 2024 Huawei Device Co., Ltd.
 *
 * HDF is dual licensed: you can use it either under the terms of
 * the GPL, or the BSD license, at your option.
 * See the LICENSE file in the root of this repository for complete details.
 */

#include "../sensor_common.hcs"

root {
    SensorConfig {
        /*
         * MS901M加速度传感器配置
         * 从UART接口读取陀螺仪和加速度计数据，提取加速度部分
         */
        accel_controller :: sensor_controller {
            accel_chip :: sensor_chip {
                ms901m_device :: sensor_device {
                    deviceName = "accelerometer";
                    deviceId = 0;
                    busType = 3;  /* UART总线类型 */
                    busNum = 5;   /* UART5 */
                    addr = 0;     /* UART设备地址 */
                    irqNum = 0;
                    irqShare = 0;
                    regSize = 1;
                    sensorTypeId = 1;  /* 加速度传感器类型ID */
                    sensorId = 1;
                    vendorName = "MS901M";
                    chipName = "MS901M";
                    chipId = 0x901;
                    chipVersion = 1;
                    powerOnDelay = 50;   /* 上电延时50ms */
                    powerOffDelay = 10;  /* 断电延时10ms */
                    minDelay = 50000000;   /* 最小采样间隔50ms */
                    maxDelay = 1000000000; /* 最大采样间隔1s */
                    fifoMaxEventCount = 16;
                    maxRange = 16;       /* 最大量程16G */
                    resolution = 0.001;  /* 分辨率1mg */
                    power = 0.1;         /* 功耗0.1mW */
                }
            }
        }

        /* 传感器初始化配置组 */
        accel_init_group :: sensor_reg_config {
            /* UART配置寄存器组 */
            regAddr = [
                0x00,  /* 波特率配置 */
                0x01,  /* 数据位配置 */
                0x02,  /* 停止位配置 */
                0x03   /* 校验位配置 */
            ];
            regValue = [
                0x07,  /* 115200波特率 */
                0x08,  /* 8数据位 */
                0x01,  /* 1停止位 */
                0x00   /* 无校验 */
            ];
            regMask = [0xFF, 0xFF, 0xFF, 0xFF];
            regLen = [1, 1, 1, 1];
            regDelay = [0, 0, 0, 0];
            regOpsType = [2, 2, 2, 2];  /* 写操作 */
            regCalType = [0, 0, 0, 0];
            regShiftNum = [0, 0, 0, 0];
            regDebug = [0, 0, 0, 0];
            regSave = [0, 0, 0, 0];
        }

        /* 传感器使能配置组 */
        accel_enable_group :: sensor_reg_config {
            regAddr = [0x10];      /* 使能寄存器 */
            regValue = [0x01];     /* 使能值 */
            regMask = [0xFF];
            regLen = [1];
            regDelay = [10];       /* 延时10ms */
            regOpsType = [2];      /* 写操作 */
            regCalType = [0];
            regShiftNum = [0];
            regDebug = [0];
            regSave = [0];
        }

        /* 传感器禁用配置组 */
        accel_disable_group :: sensor_reg_config {
            regAddr = [0x10];      /* 使能寄存器 */
            regValue = [0x00];     /* 禁用值 */
            regMask = [0xFF];
            regLen = [1];
            regDelay = [10];       /* 延时10ms */
            regOpsType = [2];      /* 写操作 */
            regCalType = [0];
            regShiftNum = [0];
            regDebug = [0];
            regSave = [0];
        }

        /* 传感器方向配置 */
        accel_direction_config :: sensor_direction_config {
            /* 坐标轴方向配置：X、Y、Z轴的符号和映射 */
            direction = [
                1,  /* X轴符号：正 */
                1,  /* Y轴符号：正 */
                1,  /* Z轴符号：正 */
                0,  /* X轴映射到X轴 */
                1,  /* Y轴映射到Y轴 */
                2   /* Z轴映射到Z轴 */
            ];
        }
    }
}
