#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <string.h>
#include <malloc.h>

#include "../common/common.h"
#include "portstat.h"

#define PORTSTAT_PAYLOAD_SIZE 8


// 将 full_port_stat 数据转换为 JSON 字符串
char *full_port_stat_to_json(full_port_stat *data) {
    cJSON *json = cJSON_CreateObject();
    if (json == NULL) return NULL;

    cJSON_AddStringToObject(json, "source", "full_port_stat");

    cJSON *data_json = cJSON_CreateObject();
    if (data_json == NULL) {
        cJSON_Delete(json);
        return NULL;
    }

    cJSON_AddItemToObject(data_json, "port_stat", port_stat_data_to_json_helper(&data->port_stat));
    cJSON_AddItemToObject(data_json, "adc_voltage",
                          port_stat_voltage_to_json_helper(&data->adc_voltage));

    cJSON_AddItemToObject(json, "data", data_json);

    char *json_string = cJSON_Print(json);
    cJSON_Delete(json);

    return json_string;
}

// 辅助函数：将 port_stat_data 转换为 cJSON 对象
cJSON *port_stat_data_to_json_helper(port_stat_data *data) {
    cJSON *json = cJSON_CreateObject();
    if (json == NULL) return NULL;

    cJSON_AddNumberToObject(json, "d0", data->d0);
    cJSON_AddNumberToObject(json, "d1", data->d1);
    cJSON_AddNumberToObject(json, "d2", data->d2);
    cJSON_AddNumberToObject(json, "d3", data->d3);

    return json;
}

// 辅助函数：将 port_stat_voltage 转换为 cJSON 对象
cJSON *port_stat_voltage_to_json_helper(port_stat_voltage *data) {
    cJSON *json = cJSON_CreateObject();
    if (json == NULL) return NULL;

    cJSON_AddNumberToObject(json, "ud0", data->ud0);
    cJSON_AddNumberToObject(json, "ud1", data->ud1);
    cJSON_AddNumberToObject(json, "ud2", data->ud2);
    cJSON_AddNumberToObject(json, "ud3", data->ud3);

    return json;
}

// 将 port_stat_data 转换为 JSON 字符串
char *port_stat_to_json(port_stat_data *data) {
    cJSON *json = cJSON_CreateObject();
    if (json == NULL) return NULL;

    cJSON_AddStringToObject(json, "source", "port_stat");

    cJSON *data_json = port_stat_data_to_json_helper(data);
    if (data_json == NULL) {
        cJSON_Delete(json);
        return NULL;
    }

    cJSON_AddItemToObject(json, "data", data_json);

    char *json_string = cJSON_Print(json);
    cJSON_Delete(json);

    return json_string;
}

// 将 port_stat_data 转换为 full_port_stat
full_port_stat *port_stat_to_full_port_stat(port_stat_data *portStat) {
    full_port_stat *data = (full_port_stat *) malloc(sizeof(full_port_stat));
    if (data == NULL) return NULL;

    data->port_stat = *portStat;
    data->adc_voltage = port_stat_to_adc_voltage(portStat);

    return data;
}

// 将 port_stat_data 转换为 port_stat_voltage
port_stat_voltage port_stat_to_adc_voltage(port_stat_data *portStat) {
    port_stat_voltage voltage;
    voltage.ud0 = (double) portStat->d0 / 4095.0 * 3.3;
    voltage.ud1 = (double) portStat->d1 / 4095.0 * 3.3;
    voltage.ud2 = (double) portStat->d2 / 4095.0 * 3.3;
    voltage.ud3 = (double) portStat->d3 / 4095.0 * 3.3;
    return voltage;
}

// 从二进制数据解码端口状态数据  
port_stat_data *decode_portstat(uint8_t *payload) {
    if (payload == NULL || malloc_usable_size(payload) != PORTSTAT_PAYLOAD_SIZE) {
        printf("[%s, %d]\n", __FUNCTION__, __LINE__);
        return NULL;
    }

    port_stat_data *portstatData = (port_stat_data *) malloc(sizeof(port_stat_data));
    if (portstatData == NULL) return NULL;

    portstatData->d0 = (uint16_t)(payload[1] << 8) | (uint16_t) payload[0];
    portstatData->d1 = (uint16_t)(payload[3] << 8) | (uint16_t) payload[2];
    portstatData->d2 = (uint16_t)(payload[5] << 8) | (uint16_t) payload[4];
    portstatData->d3 = (uint16_t)(payload[7] << 8) | (uint16_t) payload[6];

    return portstatData;
}


int process_portstat_data(uint8_t *payload) {
    //printf("[%s, %d] payload size = %d \n", __FUNCTION__, __LINE__, malloc_usable_size(payload));
    if (payload == NULL) {
        printf("Payload is NULL\n");
    }
    //print_buf_with_hex(payload, PORTSTAT_PAYLOAD_SIZE);

    port_stat_data *portStat = decode_portstat(payload);
    if (portStat != NULL) {
        char *json_string = port_stat_to_json(portStat);
        if (json_string != NULL) {
            printf("PortStat JSON: %s\n", json_string);
            free(json_string);
        }

        full_port_stat *fullPortStat = port_stat_to_full_port_stat(portStat);
        if (fullPortStat != NULL) {
            char *full_json_string = full_port_stat_to_json(fullPortStat);
            if (full_json_string != NULL) {
                printf("full_port_stat JSON: %s\n", full_json_string);
                free(full_json_string);
            }
            free(fullPortStat);
        }

        free(portStat);
    }

    return 0;
}