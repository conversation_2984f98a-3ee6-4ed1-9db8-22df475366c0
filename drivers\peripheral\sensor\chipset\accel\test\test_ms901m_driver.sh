#!/bin/bash

# MS901M加速度传感器驱动测试脚本
# 用于全面测试驱动功能和系统集成

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试配置
SENSOR_MODULE="ms901m"
SENSOR_DEVICE="/dev/sensor_accel_ms901m"
TEST_PROGRAM="./ms901m_accel_test"
LOG_FILE="/tmp/ms901m_test.log"

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_error "请以root用户运行此脚本"
        exit 1
    fi
}

# 检查驱动模块是否加载
check_driver_loaded() {
    print_header "检查驱动模块状态"
    
    if lsmod | grep -q "$SENSOR_MODULE"; then
        print_success "MS901M驱动模块已加载"
        lsmod | grep "$SENSOR_MODULE"
    else
        print_warning "MS901M驱动模块未加载"
        
        # 尝试加载模块
        print_info "尝试加载驱动模块..."
        if modprobe "$SENSOR_MODULE" 2>/dev/null; then
            print_success "驱动模块加载成功"
        else
            print_error "驱动模块加载失败"
            print_info "请检查:"
            print_info "1. 模块是否已编译: find /lib/modules -name '*ms901m*'"
            print_info "2. 内核配置是否正确: CONFIG_DRIVERS_HDF_SENSOR_ACCEL_MS901M=y"
            return 1
        fi
    fi
}

# 检查设备节点
check_device_node() {
    print_header "检查设备节点"
    
    if [ -e "$SENSOR_DEVICE" ]; then
        print_success "设备节点存在: $SENSOR_DEVICE"
        ls -l "$SENSOR_DEVICE"
        
        # 检查权限
        if [ -r "$SENSOR_DEVICE" ]; then
            print_success "设备节点可读"
        else
            print_warning "设备节点不可读，尝试修改权限..."
            chmod 644 "$SENSOR_DEVICE"
        fi
    else
        print_error "设备节点不存在: $SENSOR_DEVICE"
        print_info "请检查:"
        print_info "1. 设备树配置是否正确"
        print_info "2. HCS配置是否正确"
        print_info "3. 驱动是否正确初始化"
        return 1
    fi
}

# 检查传感器列表
check_sensor_list() {
    print_header "检查系统传感器列表"
    
    if [ -d "/sys/class/sensor" ]; then
        print_info "系统传感器列表:"
        for sensor in /sys/class/sensor/sensor*; do
            if [ -d "$sensor" ]; then
                name=$(cat "$sensor/name" 2>/dev/null || echo "unknown")
                type=$(cat "$sensor/type" 2>/dev/null || echo "unknown")
                echo "  $(basename $sensor): $name (type: $type)"
            fi
        done
    else
        print_warning "传感器类目录不存在: /sys/class/sensor"
    fi
}

# 检查UART设备
check_uart_device() {
    print_header "检查UART设备"
    
    print_info "可用的UART设备:"
    ls -l /dev/ttyS* 2>/dev/null || print_warning "未找到UART设备"
    
    # 检查UART5 (MS901M默认使用)
    if [ -e "/dev/ttyS5" ]; then
        print_success "UART5设备存在: /dev/ttyS5"
        ls -l /dev/ttyS5
    else
        print_warning "UART5设备不存在，MS901M可能使用模拟模式"
    fi
}

# 编译测试程序
compile_test_program() {
    print_header "编译测试程序"
    
    if [ ! -f "ms901m_accel_test.c" ]; then
        print_error "测试程序源码不存在: ms901m_accel_test.c"
        return 1
    fi
    
    print_info "编译测试程序..."
    if make clean && make; then
        print_success "测试程序编译成功"
    else
        print_error "测试程序编译失败"
        return 1
    fi
}

# 运行基本功能测试
run_basic_test() {
    print_header "运行基本功能测试"
    
    if [ ! -x "$TEST_PROGRAM" ]; then
        print_error "测试程序不存在或不可执行: $TEST_PROGRAM"
        return 1
    fi
    
    print_info "运行基本功能测试..."
    if timeout 30 "$TEST_PROGRAM" -b 2>&1 | tee -a "$LOG_FILE"; then
        print_success "基本功能测试通过"
    else
        print_error "基本功能测试失败"
        return 1
    fi
}

# 运行连续采集测试
run_continuous_test() {
    print_header "运行连续采集测试"
    
    print_info "运行10秒连续采集测试..."
    if timeout 15 "$TEST_PROGRAM" -c 2>&1 | tee -a "$LOG_FILE"; then
        print_success "连续采集测试完成"
    else
        print_warning "连续采集测试可能有问题"
    fi
}

# 性能测试
run_performance_test() {
    print_header "运行性能测试"
    
    print_info "测试传感器读取性能..."
    
    # 使用dd测试读取速度
    if timeout 5 dd if="$SENSOR_DEVICE" of=/dev/null bs=12 count=100 2>&1 | tee -a "$LOG_FILE"; then
        print_success "性能测试完成"
    else
        print_warning "性能测试可能有问题"
    fi
}

# 检查系统日志
check_system_logs() {
    print_header "检查系统日志"
    
    print_info "最近的MS901M相关日志:"
    if command -v dmesg >/dev/null; then
        dmesg | grep -i "ms901m\|accel\|sensor" | tail -10 || print_info "未找到相关日志"
    fi
    
    if command -v hilog >/dev/null; then
        print_info "HiLog中的传感器日志:"
        hilog | grep -i "ms901m\|accel\|sensor" | tail -10 || print_info "未找到相关日志"
    fi
}

# 生成测试报告
generate_report() {
    print_header "生成测试报告"
    
    local report_file="/tmp/ms901m_test_report.txt"
    
    cat > "$report_file" << EOF
MS901M加速度传感器驱动测试报告
=====================================

测试时间: $(date)
测试主机: $(hostname)
内核版本: $(uname -r)

驱动状态:
$(lsmod | grep "$SENSOR_MODULE" || echo "驱动未加载")

设备节点:
$(ls -l "$SENSOR_DEVICE" 2>/dev/null || echo "设备节点不存在")

UART设备:
$(ls -l /dev/ttyS5 2>/dev/null || echo "UART5不存在")

测试日志:
$(cat "$LOG_FILE" 2>/dev/null || echo "无测试日志")

EOF
    
    print_success "测试报告已生成: $report_file"
    print_info "可以使用以下命令查看完整报告:"
    print_info "cat $report_file"
}

# 清理测试环境
cleanup() {
    print_header "清理测试环境"
    
    # 清理临时文件
    rm -f "$LOG_FILE"
    
    print_info "清理完成"
}

# 显示帮助信息
show_help() {
    cat << EOF
MS901M加速度传感器驱动测试脚本

用法: $0 [选项]

选项:
  -h, --help          显示帮助信息
  -c, --check-only    只检查驱动状态，不运行测试
  -t, --test-only     只运行测试，不检查环境
  -p, --performance   运行性能测试
  -r, --report        生成详细测试报告
  -v, --verbose       详细输出模式

示例:
  $0                  # 运行完整测试
  $0 -c               # 只检查驱动状态
  $0 -t               # 只运行功能测试
  $0 -p               # 运行性能测试
  $0 -r               # 生成测试报告

EOF
}

# 主函数
main() {
    local check_only=0
    local test_only=0
    local performance=0
    local report=0
    local verbose=0
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -c|--check-only)
                check_only=1
                shift
                ;;
            -t|--test-only)
                test_only=1
                shift
                ;;
            -p|--performance)
                performance=1
                shift
                ;;
            -r|--report)
                report=1
                shift
                ;;
            -v|--verbose)
                verbose=1
                set -x
                shift
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    print_header "MS901M加速度传感器驱动测试"
    
    # 检查root权限
    check_root
    
    # 初始化日志文件
    echo "MS901M测试开始: $(date)" > "$LOG_FILE"
    
    # 环境检查
    if [ $test_only -eq 0 ]; then
        check_driver_loaded || exit 1
        check_device_node || exit 1
        check_sensor_list
        check_uart_device
    fi
    
    # 功能测试
    if [ $check_only -eq 0 ]; then
        compile_test_program || exit 1
        run_basic_test || exit 1
        run_continuous_test
        
        if [ $performance -eq 1 ]; then
            run_performance_test
        fi
    fi
    
    # 系统日志检查
    check_system_logs
    
    # 生成报告
    if [ $report -eq 1 ]; then
        generate_report
    fi
    
    print_success "所有测试完成!"
    print_info "详细日志: $LOG_FILE"
}

# 捕获退出信号进行清理
trap cleanup EXIT

# 运行主函数
main "$@"
