# Copyright (c) 2024 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/ohos.gni")

# MS901M加速度传感器测试程序
ohos_executable("ms901m_accel_test") {
  sources = [ "ms901m_accel_test.c" ]

  include_dirs = [
    "//base/hiviewdfx/hilog/interfaces/native/innerkits/include",
    "//utils/native/base/include",
    "//third_party/bounds_checking_function/include",
  ]

  deps = [
    "//base/hiviewdfx/hilog/interfaces/native/innerkits:libhilog",
    "//third_party/bounds_checking_function:libsec_shared",
    "//utils/native/base:utils",
  ]

  external_deps = [
    "hiviewdfx_hilog_native:libhilog",
  ]

  cflags = [
    "-Wall",
    "-Wextra",
    "-Werror",
    "-std=c99",
  ]

  install_enable = true
  part_name = "drivers_peripheral_sensor"
  subsystem_name = "hdf"
}

# 传感器数据监控工具（简化版C实现）
ohos_executable("ms901m_data_monitor") {
  sources = [ "ms901m_data_monitor_simple.c" ]

  include_dirs = [
    "//base/hiviewdfx/hilog/interfaces/native/innerkits/include",
    "//utils/native/base/include",
    "//third_party/bounds_checking_function/include",
  ]

  deps = [
    "//base/hiviewdfx/hilog/interfaces/native/innerkits:libhilog",
    "//third_party/bounds_checking_function:libsec_shared",
    "//utils/native/base:utils",
  ]

  external_deps = [
    "hiviewdfx_hilog_native:libhilog",
  ]

  cflags = [
    "-Wall",
    "-Wextra", 
    "-Werror",
    "-std=c99",
  ]

  install_enable = true
  part_name = "drivers_peripheral_sensor"
  subsystem_name = "hdf"
}

# 测试工具组
group("ms901m_test_tools") {
  deps = [
    ":ms901m_accel_test",
    ":ms901m_data_monitor",
  ]
}
