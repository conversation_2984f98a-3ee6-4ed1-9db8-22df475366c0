#ifndef PORTSTAT_H
#define PORTSTAT_H

#include <stdlib.h>
#include "cJSON.h"

// 定义端口状态数据和电压数据结构体
typedef struct {
    uint16_t d0;
    uint16_t d1;
    uint16_t d2;
    uint16_t d3;
} port_stat_data;

typedef struct {
    double ud0;
    double ud1;
    double ud2;
    double ud3;
} port_stat_voltage;

typedef struct {
    port_stat_data port_stat;
    port_stat_voltage adc_voltage;
} full_port_stat;

cJSON *port_stat_data_to_json_helper(port_stat_data *data);
cJSON *port_stat_voltage_to_json_helper(port_stat_voltage *data);
port_stat_voltage port_stat_to_adc_voltage(port_stat_data *portStat);
full_port_stat *port_stat_to_full_port_stat(port_stat_data *portStat);
port_stat_voltage port_stat_to_adc_voltage(port_stat_data *portStat);

port_stat_data *decode_portstat(uint8_t *payload);
int process_portstat_data(uint8_t *payload);

#endif