# MS901M加速度传感器驱动

## 概述

MS901M是一款多功能方向传感器，本驱动专门提取其中的加速度传感器数据。驱动基于OpenHarmony HDF框架开发，使用标准的加速度传感器驱动接口`sensor_accel_driver.h`。

## 特性

- **兼容性**: 完全兼容OpenHarmony加速度传感器子系统
- **简化设计**: 专注于加速度数据处理，代码简洁易维护
- **UART通信**: 通过UART接口与MS901M传感器通信
- **标准接口**: 使用标准的`AccelOpsCall`接口，与其他加速度传感器驱动保持一致

## 文件结构

```
drivers/peripheral/sensor/chipset/accel/
├── accel_ms901m.h          # 驱动头文件
├── accel_ms901m.c          # 驱动实现文件
├── ms901m_config.hcs       # HCS配置文件
├── BUILD.gn                # 构建配置文件
└── README_MS901M_zh.md     # 说明文档
```

## 技术规格

- **传感器类型**: 加速度传感器
- **传感器ID**: 1 (SENSOR_TAG_ACCELEROMETER)
- **通信接口**: UART (115200波特率)
- **数据格式**: 16位有符号整数，小端格式
- **量程**: ±2G (可扩展到±4G, ±8G, ±16G)
- **分辨率**: 1mg
- **采样频率**: 50-100Hz

## 数据格式

MS901M的GYRO数据帧(0x03)包含12字节数据：
- 前6字节: 加速度数据 (X, Y, Z轴，每轴2字节)
- 后6字节: 陀螺仪数据 (本驱动忽略)

```
字节序列: [AccX_L, AccX_H, AccY_L, AccY_H, AccZ_L, AccZ_H, GyroX_L, GyroX_H, GyroY_L, GyroY_H, GyroZ_L, GyroZ_H]
```

## 集成步骤

### 1. 添加到构建系统

编辑 `drivers/peripheral/sensor/BUILD.gn`：

```gn
deps += [
  "chipset/accel:hdf_sensor_accel_ms901m",
]
```

### 2. 配置设备树

将以下配置添加到设备信息文件：

```hcs
accel_ms901m :: device {
    device0 :: deviceNode {
        policy = 2;
        priority = 100;
        preload = 0;
        permission = 0664;
        moduleName = "HDF_SENSOR_ACCEL_MS901M";
        serviceName = "sensor_accel_ms901m";
        deviceMatchAttr = "hdf_sensor_accel_ms901m_driver";
    }
}
```

### 3. 传感器配置

将 `ms901m_config.hcs` 的内容合并到传感器配置文件中。

### 4. 编译

```bash
./build.sh --product-name {product_name} --build-target drivers_peripheral_sensor
```

## 使用示例

### 应用层调用

```c
#include "sensor_agent.h"

// 订阅加速度传感器
int32_t SubscribeAccelSensor(void)
{
    SensorUser user;
    user.callback = AccelDataCallback;
    
    // 激活加速度传感器
    int32_t ret = ActivateSensor(SENSOR_TYPE_ACCELEROMETER, &user);
    if (ret != 0) {
        printf("Failed to activate accel sensor\n");
        return ret;
    }
    
    // 设置采样间隔为100ms
    ret = SetBatch(SENSOR_TYPE_ACCELEROMETER, 100000000, 0);
    return ret;
}

// 数据回调函数
void AccelDataCallback(SensorEvent *event)
{
    if (event->sensorTypeId == SENSOR_TYPE_ACCELEROMETER) {
        float *data = (float *)event->data;
        printf("Accel: X=%.3f, Y=%.3f, Z=%.3f m/s²\n",
               data[0], data[1], data[2]);
    }
}
```

## 实际UART实现

当前驱动使用模拟数据，实际应用需要实现UART通信：

```c
// 在ReadMs901mRawData函数中添加真实UART读取
static int32_t ReadMs901mRawData(struct SensorCfgData *data, struct AccelData *rawData, uint64_t *timestamp)
{
    // 1. 打开UART设备
    DevHandle uartHandle = UartOpen(5); // UART5
    if (uartHandle == NULL) {
        return HDF_FAILURE;
    }
    
    // 2. 配置UART参数
    struct UartAttribute attr = {
        .dataBits = 8,
        .parity = UART_ATTR_PARITY_NONE,
        .stopBits = 1,
        .baudRate = 115200,
    };
    UartSetAttribute(uartHandle, &attr);
    
    // 3. 读取数据帧
    uint8_t buffer[32];
    int32_t readLen = UartRead(uartHandle, buffer, sizeof(buffer));
    
    // 4. 解析数据帧，查找GYRO帧(0x03)
    // 实现帧同步和数据提取逻辑
    
    // 5. 提取加速度数据
    if (frameId == MS901M_FRAME_GYRO) {
        return Ms901mDecodeAccelData(payload, rawData);
    }
    
    UartClose(uartHandle);
    return HDF_SUCCESS;
}
```

## 调试

### 查看日志
```bash
hilog | grep accel
hilog | grep ms901m
```

### 检查设备节点
```bash
ls -l /dev/sensor_*
```

### 测试传感器
```bash
# 使用传感器测试工具
sensor_test --type accelerometer --duration 10
```

## 注意事项

1. **硬件连接**: 确保MS901M正确连接到UART5
2. **波特率匹配**: 确认MS901M配置为115200波特率
3. **数据同步**: 实现可靠的帧同步机制
4. **错误处理**: 添加UART通信异常处理
5. **性能优化**: 根据需要调整采样频率

## 扩展功能

如需支持其他传感器数据（陀螺仪、磁力计等），可以：

1. 创建对应的传感器驱动文件
2. 使用相应的传感器驱动框架
3. 共享UART通信逻辑
4. 根据帧ID分发数据到不同传感器

## 技术支持

参考文档：
- OpenHarmony传感器子系统开发指南
- HDF驱动框架文档
- `drivers/peripheral/sensor/chipset/accel/accel_mxc6655xa.c` 参考实现
