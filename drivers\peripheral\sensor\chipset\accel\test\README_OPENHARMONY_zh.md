# MS901M传感器测试工具 - OpenHarmony版本

## 概述

本目录包含了专为OpenHarmony系统设计的MS901M加速度传感器测试工具，完全集成到OH构建系统中。

## 文件结构

```
drivers/peripheral/sensor/chipset/accel/test/
├── BUILD.gn                        # OpenHarmony构建配置
├── bundle.json                     # 组件配置文件
├── ms901m_accel_test.c             # 主测试程序
├── ms901m_data_monitor_simple.c    # 数据监控工具
├── README_OPENHARMONY_zh.md        # OpenHarmony使用说明
└── Makefile                        # 已废弃，仅作参考
```

## 编译方法

### 1. 编译测试工具

```bash
# 编译特定测试工具
./build.sh --product-name rk3568 --build-target ms901m_accel_test
./build.sh --product-name rk3568 --build-target ms901m_data_monitor

# 编译所有测试工具
./build.sh --product-name rk3568 --build-target ms901m_test_tools

# 编译整个系统（包含测试工具）
./build.sh --product-name rk3568
```

### 2. 编译输出

编译成功后，可执行文件位于：
```
out/rk3568/packages/phone/system/bin/
├── ms901m_accel_test      # 主测试程序
└── ms901m_data_monitor    # 数据监控工具
```

## 部署和运行

### 1. 烧录到设备

```bash
# 烧录完整镜像
hdc file send out/rk3568/packages/phone/images/system.img /path/to/device

# 或者单独推送可执行文件
hdc file send out/rk3568/packages/phone/system/bin/ms901m_accel_test /system/bin/
hdc file send out/rk3568/packages/phone/system/bin/ms901m_data_monitor /system/bin/
```

### 2. 在设备上运行

```bash
# 连接设备
hdc shell

# 运行主测试程序
ms901m_accel_test

# 运行数据监控工具
ms901m_data_monitor

# 查看帮助
ms901m_accel_test -h
ms901m_data_monitor -h
```

## 测试程序功能

### 1. ms901m_accel_test

**功能**：
- 基本功能测试：验证设备打开、数据读取
- 连续采集测试：长时间数据采集和统计分析
- 完整的错误处理和日志记录

**使用方法**：
```bash
# 运行所有测试
ms901m_accel_test

# 只运行基本功能测试
ms901m_accel_test -b

# 只运行连续采集测试
ms901m_accel_test -c

# 显示帮助
ms901m_accel_test -h
```

**输出示例**：
```
=== 基本功能测试 ===
成功打开传感器设备: /dev/sensor_accel_ms901m
成功读取传感器数据:
  X轴: 50 mg
  Y轴: 100 mg
  Z轴: 1000 mg
  时间戳: 1234567890123456 ns
基本功能测试通过

=== 连续数据采集测试 ===
测试时长: 10 秒
采样间隔: 100 ms
样本 100: X=   52, Y=  102, Z= 1005 mg

=== MS901M加速度传感器测试统计 ===
测试时长: 10.05 秒
总采样数: 100
有效采样: 98
错误采样: 2
采样率: 9.95 Hz
成功率: 98.00%

--- 加速度数据统计 (mg) ---
X轴: 平均=51.20, 范围=[45, 58]
Y轴: 平均=101.50, 范围=[95, 108]
Z轴: 平均=1002.30, 范围=[995, 1010]
重力加速度模长: 1005.23 mg (理论值: 1000mg)
```

### 2. ms901m_data_monitor

**功能**：
- 实时数据监控
- 统计分析和质量评估
- 可配置监控时长和采样间隔

**使用方法**：
```bash
# 默认监控30秒
ms901m_data_monitor

# 监控60秒
ms901m_data_monitor -t 60

# 监控10秒，50ms采样间隔
ms901m_data_monitor -t 10 -i 50

# 显示帮助
ms901m_data_monitor -h
```

**输出示例**：
```
=== MS901M传感器实时监控 ===
监控时长: 30 秒
采样间隔: 100 ms
按 Ctrl+C 提前停止

样本 300: X=    51, Y=   102, Z=  1003 mg

监控完成

=== MS901M传感器监控统计 ===
监控时长: 30.12 秒
总采样数: 300
有效采样: 298
错误采样: 2
采样率: 9.96 Hz
成功率: 99.33%

--- 加速度数据统计 (mg) ---
X轴: 平均=50.85, 范围=[45, 58]
Y轴: 平均=101.20, 范围=[95, 108]
Z轴: 平均=1001.50, 范围=[995, 1010]
重力加速度模长: 1004.12 mg (理论值: 1000mg)
数据质量: 优秀 (Z轴误差 < 50mg)
```

## 日志查看

### 1. HiLog日志

```bash
# 查看测试程序日志
hilog | grep MS901M_TEST
hilog | grep MS901M_MONITOR

# 查看传感器驱动日志
hilog | grep ms901m
hilog | grep accel
```

### 2. 内核日志

```bash
# 查看内核日志
dmesg | grep ms901m
dmesg | grep sensor
```

## 故障排除

### 1. 编译问题

**问题**：编译失败，找不到头文件
**解决**：
```bash
# 检查依赖组件是否存在
ls -la base/hiviewdfx/hilog/interfaces/native/innerkits/include/
ls -la utils/native/base/include/

# 重新同步代码
repo sync
```

**问题**：链接失败，找不到库文件
**解决**：
```bash
# 检查依赖库是否编译
./build.sh --product-name rk3568 --build-target hilog
./build.sh --product-name rk3568 --build-target utils_base
```

### 2. 运行问题

**问题**：设备上找不到可执行文件
**解决**：
```bash
# 检查文件是否正确编译
ls -la out/rk3568/packages/phone/system/bin/ms901m*

# 检查文件是否正确部署
hdc shell ls -la /system/bin/ms901m*

# 手动推送文件
hdc file send out/rk3568/packages/phone/system/bin/ms901m_accel_test /system/bin/
```

**问题**：权限拒绝
**解决**：
```bash
# 检查文件权限
hdc shell ls -la /system/bin/ms901m*

# 修改权限
hdc shell chmod 755 /system/bin/ms901m_accel_test
```

**问题**：传感器设备不存在
**解决**：
```bash
# 检查驱动是否加载
hdc shell lsmod | grep ms901m

# 检查设备节点
hdc shell ls -la /dev/sensor*

# 检查设备树配置
hdc shell cat /proc/device-tree/sensor_host/*/compatible
```

### 3. 数据异常

**问题**：读取数据全为0
**解决**：
```bash
# 检查UART设备
hdc shell ls -la /dev/ttyS*

# 检查驱动日志
hdc shell hilog | grep ms901m

# 手动测试设备读取
hdc shell hexdump -C /dev/sensor_accel_ms901m | head
```

## 性能优化

### 1. 编译优化

在BUILD.gn中添加优化选项：
```gn
cflags += [
  "-O2",           # 优化级别
  "-DNDEBUG",      # 禁用调试信息
]
```

### 2. 运行时优化

```bash
# 设置CPU调度策略
hdc shell echo performance > /sys/devices/system/cpu/cpu0/cpufreq/scaling_governor

# 设置进程优先级
hdc shell renice -10 $(pidof ms901m_accel_test)
```

## 集成到产品

### 1. 添加到产品配置

在产品配置文件中添加测试工具：
```json
{
  "subsystem": "hdf",
  "components": [
    {
      "component": "drivers_peripheral_sensor",
      "features": [
        "ms901m_sensor_test_support = true"
      ]
    }
  ]
}
```

### 2. 自动化测试

创建自动化测试脚本：
```bash
#!/bin/bash
# 自动化测试脚本

# 运行基本测试
hdc shell ms901m_accel_test -b

# 运行短时间监控
hdc shell ms901m_data_monitor -t 5

# 检查测试结果
if [ $? -eq 0 ]; then
    echo "测试通过"
else
    echo "测试失败"
    exit 1
fi
```

## 总结

OpenHarmony版本的MS901M测试工具提供了：

1. **完整的OH集成**：使用BUILD.gn构建，支持HiLog日志
2. **标准化接口**：符合OpenHarmony应用开发规范
3. **易于部署**：集成到系统镜像，一键部署
4. **全面测试**：覆盖功能、性能、稳定性测试
5. **友好调试**：详细的日志和错误信息

通过这些工具，可以在OpenHarmony设备上方便地测试和验证MS901M传感器驱动的功能。
