# Copyright (c) 2024 Huawei Device Co., Ltd.
#
# HDF is dual licensed: you can use it either under the terms of
# the GPL, or the BSD license, at your option.
# See the LICENSE file in the root of this repository for complete details.

import("//build/ohos.gni")
import("//drivers/hdf_core/adapter/uhdf2/uhdf.gni")

ohos_shared_library("libaccel_ms901m") {
  include_dirs = [
    ".",
    "//drivers/hdf_core/framework/include",
    "//drivers/hdf_core/framework/include/osal",
    "//drivers/hdf_core/framework/include/utils",
    "//drivers/hdf_core/framework/utils/include",
    "//drivers/hdf_core/framework/include/core",
    "//drivers/hdf_core/framework/core/common/include/manager",
    "//drivers/hdf_core/framework/core/host/include",
    "//drivers/hdf_core/framework/core/master/include",
    "//drivers/hdf_core/framework/core/shared/include",
    "//drivers/hdf_core/framework/core/adapter/vnode/include",
    "//drivers/hdf_core/framework/ability/sbuf/include",
    "//drivers/hdf_core/framework/include/platform",
    "//drivers/hdf_core/framework/include/config",
    "//drivers/hdf_core/framework/model/sensor/driver/include",
    "//drivers/hdf_core/framework/model/sensor/driver/common/include",
    "//drivers/hdf_core/framework/model/sensor/driver/accel",
    "//drivers/hdf_core/framework/support/platform/include",
    "//drivers/peripheral/sensor/interfaces/include",
    "//drivers/peripheral/sensor/hal/include",
    "//third_party/bounds_checking_function/include",
  ]

  sources = [ "accel_ms901m.c" ]

  deps = [
    "//drivers/hdf_core/adapter/uhdf2/manager:hdf_core",
    "//drivers/hdf_core/adapter/uhdf2/osal:hdf_osal",
    "//drivers/hdf_core/adapter/uhdf2/platform:hdf_platform",
    "//drivers/hdf_core/framework/model/sensor/driver:hdf_sensor_driver",
    "//third_party/bounds_checking_function:libsec_shared",
  ]

  if (is_standard_system) {
    external_deps = [
      "c_utils:utils",
      "hilog:libhilog",
    ]
  } else {
    external_deps = [ "hilog:libhilog" ]
  }

  install_images = [ chipset_base_dir ]
  subsystem_name = "hdf"
  part_name = "drivers_peripheral_sensor"
}

group("hdf_sensor_accel_ms901m") {
  deps = [ ":libaccel_ms901m" ]
}
