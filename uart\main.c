#include <stdio.h>  
#include <stdlib.h>  
#include <string.h>  
#include <unistd.h>  
#include <fcntl.h>  
#include <termios.h>
#include <errno.h>
#include <unistd.h>
#include <sys/epoll.h>

#include "data_parser/euler.h"
#include "data_parser/quaternion.h"
#include "data_parser/gyro.h"
#include "data_parser/magnetic.h"
#include "data_parser/pressure.h"
#include "data_parser/portstat.h"
#include "serial_port//serial_port.h"
#include "common/common.h"


// 读取字符函数  
int read_char(serial_port* port, unsigned char* buf, int size) {
    int n = serial_read(port, buf, size);  
    if (n < 0) {  
        printf("read_char error\n");
        return -1;  
    }  
    if (n == 0) {  
        // EOF  
        return -2;  
    }  
    return 0;
}  
  
// 校验和检查函数  
int checksum_check(unsigned char* payload, int length) {
    unsigned char checksum = payload[length - 1];  
    unsigned char actualSum = 0;  
    for (int i = 0; i < length - 1; i++) {  
        actualSum += payload[i];  
    }  
    return checksum == actualSum;  
}  
  
// 解码开始函数  
int decode_start(serial_port* port) {
    unsigned char one_byte[1];
    unsigned char next_byte[1];
    unsigned char frame_id_byte[1];
    unsigned char data_length_byte[1];
      
    if (read_char(port, one_byte, 1) != 0 || one_byte[0] != 0x55) {
        return -1; // desync case  
    }  
    if (read_char(port, next_byte, 1) != 0 || next_byte[0] != 0x55) {
        return -1; // desync case  
    }  
	//printf("sync frame header success\n");
    // 已同步到帧头  
    if (read_char(port, frame_id_byte, 1) != 0) {
        return -1;  
    }
  
    unsigned char frame_id = frame_id_byte[0];
  
    if (read_char(port, data_length_byte, 1) != 0) {
        return -1;  
    }  
  
    int data_length = data_length_byte[0];
    unsigned char* data_block = (unsigned char*)malloc(data_length);
    if (read_char(port, data_block, data_length) != 0) {
        free(data_block);
        return -1;
    }  
  
    unsigned char checksum_byte[1];
    if (read_char(port, checksum_byte, 1) != 0) {
        free(data_block);
        return -1;  
    }  
  
    // 组合整个有效载荷  
    unsigned char whole_payload[1 + 1 + 1 + 1 + data_length + 1];
    memcpy(whole_payload, one_byte, 1);
    memcpy(whole_payload + 1, next_byte, 1);
    memcpy(whole_payload + 2, frame_id_byte, 1);
    memcpy(whole_payload + 3, data_length_byte, 1);
    memcpy(whole_payload + 4, data_block, data_length);
    memcpy(whole_payload + 4 + data_length, checksum_byte, 1);
  
    if (!checksum_check(whole_payload, sizeof(whole_payload))) {
        free(data_block);
        return -1; // checksum failed  
    }
	//printf("get frame_id = %d, data_length = %d\n", frame_id, data_length);
  
    // 根据frame_id处理数据
    switch (frame_id) {
        case EULER:  //欧拉角，自姿态角

            process_euler_data(data_block);
            break;  
        case QUATERNION:  // 四元数

            process_quaternion_data(data_block);
            break;  
        case GYRO:  // 陀螺仪和加速度计

            process_gyro_data(data_block);
            break;  
        case MAGNETIC:  // 磁力计

            process_magnetic_data(data_block);
            break;  
        case PRESSURE:  // 气压计

            process_press_data(data_block);
            break;  
        case PORTSTAT:  // 端口状态

            process_portstat_data(data_block);
            break;  
        default:  
            printf("MS901M: received unknown frame: frame_id=0x%.2X\n", frame_id);
            free(data_block);
            return -1; // unknown Data type  
    }  
  
    free(data_block);
    return 0;  
}

int main(int argc, char **argv)
{
  	if (argc != 3) {
		printf("usage : uart_test /dev/ttyS5 115200\n");
		return -1;
	}
	int baudrate = atoi(argv[2]);
    
    // 初始化串口
    serial_port port;  
    serial_open(&port, argv[1], baudrate);
	while (1) {
		// 解码和处理数据  
        decode_start(&port);
		usleep(50000);//500ms 加点延时，降低CPU占用率
	}
    // 关闭串口  
    serial_close(&port);  
  
    return 0;  
}