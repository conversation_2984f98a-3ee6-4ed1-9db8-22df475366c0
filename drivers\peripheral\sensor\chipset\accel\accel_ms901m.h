/*
 * Copyright (c) 2024 Huawei Device Co., Ltd.
 *
 * HDF is dual licensed: you can use it either under the terms of
 * the GPL, or the BSD license, at your option.
 * See the LICENSE file in the root of this repository for complete details.
 */

#ifndef ACCEL_MS901M_H
#define ACCEL_MS901M_H

#include "sensor_accel_driver.h"
#include "sensor_config_parser.h"

/* MS901M数据帧格式定义 */
#define MS901M_FRAME_HEADER1    0x55
#define MS901M_FRAME_HEADER2    0x55
#define MS901M_FRAME_GYRO       0x03    /* 陀螺仪和加速度计数据帧 */
#define MS901M_GYRO_DATA_LEN    12      /* GYRO帧数据长度 */

/* UART配置参数 */
#define MS901M_UART_BAUDRATE    115200  /* 默认波特率 */
#define MS901M_UART_DATA_BITS   8       /* 数据位 */
#define MS901M_UART_STOP_BITS   1       /* 停止位 */
#define MS901M_UART_PARITY      0       /* 无校验 */

/* 加速度传感器量程和精度 */
#define MS901M_ACCEL_RANGE_2G           2
#define MS901M_ACCEL_RANGE_4G           4
#define MS901M_ACCEL_RANGE_8G           8
#define MS901M_ACCEL_RANGE_16G          16

/* 加速度传感器灵敏度 (mg/LSB) */
#define MS901M_ACC_SENSITIVITY_2G       1
#define MS901M_ACC_SENSITIVITY_4G       2
#define MS901M_ACC_SENSITIVITY_8G       4
#define MS901M_ACC_SENSITIVITY_16G      8

/* 默认采样频率 */
#define MS901M_ACCEL_DEFAULT_ODR_100HZ  100
#define MS901M_ACCEL_DEFAULT_ODR_50HZ   50

/* MS901M驱动数据结构 */
struct Ms901mDrvData {
    struct IDeviceIoService ioService;
    struct HdfDeviceObject *device;
    struct SensorCfgData *sensorCfg;
};

#endif /* ACCEL_MS901M_H */
