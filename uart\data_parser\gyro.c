#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <string.h>
#include <malloc.h>
#include "../common/common.h"
#include "gyro.h"


#define GYRO_PAYLOAD_SIZE 12

// gyro_data 转换为 JSON 字符串（简化版）
char *gyro_data_to_json(gyro_data *data) {
    // 这是一个简化的 JSON 创建过程，实际应用中可能需要使用 JSON 库  
    char *json = (char *) malloc(512 * sizeof(char));
    if (json == NULL) {
        return NULL; // 内存分配失败  
    }
    snprintf(json, 512,
             "{\"Source\":\"gyro\",\"Data\":{\"acc_x\":%f,\"acc_y\":%f,\"acc_z\":%f,\"gyro_x\":%f,\"gyro_y\":%f,\"gyro_z\":%f}}",
             data->acc_x, data->acc_y, data->acc_z, data->gyro_x, data->gyro_y, data->gyro_z);
    return json;
}

// 获取设备陀螺仪范围（占位符数据）  
device_gyro_range get_device_gyro_range() {
    device_gyro_range gyroRange = {16, 2000};
    //printf("Gyro range data fetched from dummy data.\n");
    return gyroRange;
}

// 解码陀螺仪数据  
gyro_data *decode_gyro(uint8_t *payload, device_gyro_range gyro_range) {
    if (payload == NULL) {
        return NULL; // 错误的 payload  
    }

    gyro_data *data = (gyro_data *) malloc(sizeof(gyro_data));
    if (data == NULL) {
        return NULL; // 内存分配失败  
    }

    int16_t aXL = (int16_t)((uint16_t) payload[1] << 8 | (uint16_t) payload[0]);
    int16_t aYL = (int16_t)((uint16_t) payload[3] << 8 | (uint16_t) payload[2]);
    int16_t aZL = (int16_t)((uint16_t) payload[5] << 8 | (uint16_t) payload[4]);
    int16_t gXL = (int16_t)((uint16_t) payload[7] << 8 | (uint16_t) payload[6]);
    int16_t gYL = (int16_t)((uint16_t) payload[9] << 8 | (uint16_t) payload[8]);
    int16_t gZL = (int16_t)((uint16_t) payload[11] << 8 | (uint16_t) payload[10]);

    data->acc_x = (double) aXL / 32768.0 * (double) gyro_range.accelRange;
    data->acc_y = (double) aYL / 32768.0 * (double) gyro_range.accelRange;
    data->acc_z = (double) aZL / 32768.0 * (double) gyro_range.accelRange;
    data->gyro_x = (double) gXL / 32768.0 * (double) gyro_range.gyroRange;
    data->gyro_y = (double) gYL / 32768.0 * (double) gyro_range.gyroRange;
    data->gyro_z = (double) gZL / 32768.0 * (double) gyro_range.gyroRange;

    return data;
}

int process_gyro_data(uint8_t *payload) {
    //printf("[%s, %d] payload size = %d \n", __FUNCTION__, __LINE__, malloc_usable_size(payload));
    if (payload == NULL) {
        printf("Payload is NULL\n");
    }
    //print_buf_with_hex(payload, GYRO_PAYLOAD_SIZE);


    device_gyro_range gyro_range = get_device_gyro_range();
    gyro_data *data = decode_gyro(payload, gyro_range);
    if (data != NULL) {
        char *json = gyro_data_to_json(data);
        if (json != NULL) {
            printf("JSON: %s\n", json);
            free(json);
        }
        free(data);
    } else {
        printf("Failed to decode Gyro data\n");
    }

    return 0;
}