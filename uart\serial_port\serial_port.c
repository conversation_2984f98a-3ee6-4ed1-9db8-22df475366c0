#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <termios.h>
#include <errno.h>
#include <unistd.h>
#include "serial_port.h"


int serial_read(serial_port* port, unsigned char* buffer, int size)
{
    int bytes_read = read(port->fd, buffer, size);
    return bytes_read;
}

void serial_close(serial_port *port) { close(port->fd); }

static speed_t get_baudrate(int baudrate)
{
    switch(baudrate) {
        case 0: return B0;
        case 50: return B50;
        case 75: return B75;
        case 110: return B110;
        case 134: return B134;
        case 150: return B150;
        case 200: return B200;
        case 300: return B300;
        case 600: return B600;
        case 1200: return B1200;
        case 1800: return B1800;
        case 2400: return B2400;
        case 4800: return B4800;
        case 9600: return B9600;
        case 19200: return B19200;
        case 38400: return B38400;
        case 57600: return B57600;
        case 115200: return B115200;
        case 230400: return B230400;
        case 460800: return B460800;
        case 500000: return B500000;
        case 576000: return B576000;
        case 921600: return B921600;
        case 1000000: return B1000000;
        case 1152000: return B1152000;
        case 1500000: return B1500000;
        case 2000000: return B2000000;
        case 2500000: return B2500000;
        case 3000000: return B3000000;
        case 3500000: return B3500000;
        case 4000000: return B4000000;
        default: return -1;
    }
}
// 打开文件   设置串口的通讯
int serial_open(serial_port* port, const char* device, uint32_t baudrate)
{

    printf("The baudrate is %d\n", baudrate);

    speed_t speed;
    int databits = 8; //串口数据帧的数据位有8位
    int stopbits = 1; //数据帧的结束位1位
    int parity = 0;  //数据帧没有校验位
    int fd;

    /* Check arguments */
    {
        speed = get_baudrate(baudrate);
        if (speed == -1) {
            printf("Invalid baudrate: %d", baudrate);
            return -1;
        }
    }

    // char *argv[] = {"/dev/ttyS5", NULL};


    // 打开串口
    fd = open(device, O_RDWR | O_NOCTTY | O_NDELAY);
    if (fd == -1) {
        printf("Unable to open serial port: %s\n", device);
        return -1;
    }
    printf("open serial port %s success\n", device);
    port->fd = fd;

    /*  串口配置 Configure device */
    {
        struct termios cfg;
        printf("Configuring serial port\n");
        //cfg中的标志位都是用的二进制的掩码 MARK操作的
        // 192.168.31.48  255.255.255.0
        if (tcgetattr(fd, &cfg)) { //做掩码的复位操作
            printf("tcgetattr() failed");
            close(fd);
            return -1;
        }

        // Initialize termios struct
        cfmakeraw(&cfg); //串口通讯采用2进制的原始raw模式

        // Set data bits
        cfg.c_cflag &= ~CSIZE;
        switch (databits) {
            case 5:
                cfg.c_cflag |= CS5;
                break;
            case 6:
                cfg.c_cflag |= CS6;
                break;
            case 7:
                cfg.c_cflag |= CS7;
                break;
            case 8: //按位或操作，设置数据位的掩码为8位
                cfg.c_cflag |= CS8;
                break;
            default:
                printf("Invalid data bits\n");
                close(fd);
                return -1;
        }

        // Set stop bits
        switch (stopbits) {
            case 1:
                cfg.c_cflag &= ~CSTOPB;
                break;
            case 2:
                cfg.c_cflag |= CSTOPB;
                break;
            default:
                printf("Invalid stop bits\n");
                close(fd);
                return -1;
        }

        switch (parity) {
            case 0:
                cfg.c_cflag &= ~PARENB;    //PARITY OFF
                break;
            case 1:
                cfg.c_cflag |= (PARODD | PARENB);   //PARITY ODD
                cfg.c_iflag &= ~IGNPAR;
                cfg.c_iflag |= PARMRK;
                cfg.c_iflag |= INPCK;
                break;
            case 2:
                cfg.c_iflag &= ~(IGNPAR | PARMRK); //PARITY EVEN
                cfg.c_iflag |= INPCK;
                cfg.c_cflag |= PARENB;
                cfg.c_cflag &= ~PARODD;
                break;
            case 3:
                //  PARITY SPACE
                cfg.c_iflag &= ~IGNPAR;             //  Make sure wrong parity is not ignored
                cfg.c_iflag |= PARMRK;              //  Marks parity error, parity error
                //  is given as three char sequence
                cfg.c_iflag |= INPCK;               //  Enable input parity checking
                cfg.c_cflag |= PARENB | CMSPAR;     //  Enable parity and set space parity
                cfg.c_cflag &= ~PARODD;             //
                break;
            case 4:
                //  PARITY MARK
                cfg.c_iflag &= ~IGNPAR;             //  Make sure wrong parity is not ignored
                cfg.c_iflag |= PARMRK;              //  Marks parity error, parity error
                //  is given as three char sequence
                cfg.c_iflag |= INPCK;               //  Enable input parity checking
                cfg.c_cflag |= PARENB | CMSPAR | PARODD;
                break;
            default:
                cfg.c_cflag &= ~PARENB;
                break;
        }

        // Set baud rate
        cfsetispeed(&cfg, speed);
        cfsetospeed(&cfg, speed);

        if (tcsetattr(fd, TCSANOW, &cfg)) {
            printf("tcsetattr() failed\n");
            close(fd);
            return -1;
        }
        printf("configure success, databits =%d, stopbits=%d, parity=%d\n", databits, stopbits, parity);
    }
    return 0;
}