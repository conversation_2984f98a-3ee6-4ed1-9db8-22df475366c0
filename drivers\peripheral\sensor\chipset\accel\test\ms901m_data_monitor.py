#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
MS901M加速度传感器数据监控和可视化工具
用于实时监控传感器数据并生成图表
"""

import os
import sys
import time
import struct
import signal
import argparse
from collections import deque
import threading

# 尝试导入可选的绘图库
try:
    import matplotlib.pyplot as plt
    import matplotlib.animation as animation
    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False
    print("警告: matplotlib未安装，无法显示实时图表")
    print("安装命令: pip3 install matplotlib")

class MS901MMonitor:
    """MS901M传感器数据监控器"""
    
    def __init__(self, device_path="/dev/sensor_accel_ms901m", max_samples=1000):
        self.device_path = device_path
        self.max_samples = max_samples
        self.running = False
        
        # 数据存储
        self.timestamps = deque(maxlen=max_samples)
        self.accel_x = deque(maxlen=max_samples)
        self.accel_y = deque(maxlen=max_samples)
        self.accel_z = deque(maxlen=max_samples)
        
        # 统计信息
        self.sample_count = 0
        self.start_time = 0
        self.last_update = 0
        
        # 线程锁
        self.data_lock = threading.Lock()
        
    def read_sensor_data(self):
        """读取传感器数据"""
        try:
            with open(self.device_path, 'rb') as f:
                # 读取12字节数据 (3个int32)
                data = f.read(12)
                if len(data) == 12:
                    # 解析数据 (小端序)
                    x, y, z = struct.unpack('<iii', data)
                    return x, y, z
                else:
                    print(f"数据长度错误: {len(data)} (期望: 12)")
                    return None
        except Exception as e:
            print(f"读取传感器数据失败: {e}")
            return None
    
    def update_data(self, x, y, z):
        """更新数据缓存"""
        current_time = time.time()
        
        with self.data_lock:
            self.timestamps.append(current_time)
            self.accel_x.append(x)
            self.accel_y.append(y)
            self.accel_z.append(z)
            
            self.sample_count += 1
            self.last_update = current_time
    
    def get_statistics(self):
        """获取统计信息"""
        with self.data_lock:
            if not self.accel_x:
                return None
                
            duration = self.last_update - self.start_time if self.start_time > 0 else 0
            sample_rate = self.sample_count / duration if duration > 0 else 0
            
            stats = {
                'sample_count': self.sample_count,
                'duration': duration,
                'sample_rate': sample_rate,
                'x_avg': sum(self.accel_x) / len(self.accel_x),
                'y_avg': sum(self.accel_y) / len(self.accel_y),
                'z_avg': sum(self.accel_z) / len(self.accel_z),
                'x_min': min(self.accel_x),
                'x_max': max(self.accel_x),
                'y_min': min(self.accel_y),
                'y_max': max(self.accel_y),
                'z_min': min(self.accel_z),
                'z_max': max(self.accel_z),
            }
            
            # 计算重力加速度模长
            magnitude = (stats['x_avg']**2 + stats['y_avg']**2 + stats['z_avg']**2)**0.5
            stats['magnitude'] = magnitude
            
            return stats
    
    def print_statistics(self):
        """打印统计信息"""
        stats = self.get_statistics()
        if stats:
            print(f"\n=== MS901M传感器统计信息 ===")
            print(f"采样数量: {stats['sample_count']}")
            print(f"运行时间: {stats['duration']:.2f} 秒")
            print(f"采样率: {stats['sample_rate']:.2f} Hz")
            print(f"X轴: 平均={stats['x_avg']:.2f}, 范围=[{stats['x_min']}, {stats['x_max']}] mg")
            print(f"Y轴: 平均={stats['y_avg']:.2f}, 范围=[{stats['y_min']}, {stats['y_max']}] mg")
            print(f"Z轴: 平均={stats['z_avg']:.2f}, 范围=[{stats['z_min']}, {stats['z_max']}] mg")
            print(f"重力加速度模长: {stats['magnitude']:.2f} mg (理论值: 1000mg)")
    
    def monitor_console(self, interval=0.1, duration=None):
        """控制台监控模式"""
        print(f"开始监控MS901M传感器数据...")
        print(f"设备路径: {self.device_path}")
        print(f"采样间隔: {interval} 秒")
        if duration:
            print(f"监控时长: {duration} 秒")
        print("按 Ctrl+C 停止监控\n")
        
        self.running = True
        self.start_time = time.time()
        
        try:
            while self.running:
                data = self.read_sensor_data()
                if data:
                    x, y, z = data
                    self.update_data(x, y, z)
                    
                    # 每10个样本打印一次
                    if self.sample_count % 10 == 0:
                        print(f"\r样本 {self.sample_count}: X={x:6d}, Y={y:6d}, Z={z:6d} mg", end='', flush=True)
                
                # 检查是否达到时长限制
                if duration and (time.time() - self.start_time) >= duration:
                    break
                    
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print("\n\n监控已停止")
        finally:
            self.running = False
            self.print_statistics()
    
    def save_data_to_file(self, filename):
        """保存数据到文件"""
        with self.data_lock:
            if not self.timestamps:
                print("没有数据可保存")
                return
                
            try:
                with open(filename, 'w') as f:
                    f.write("timestamp,accel_x,accel_y,accel_z\n")
                    for i in range(len(self.timestamps)):
                        f.write(f"{self.timestamps[i]:.6f},{self.accel_x[i]},{self.accel_y[i]},{self.accel_z[i]}\n")
                print(f"数据已保存到: {filename}")
            except Exception as e:
                print(f"保存数据失败: {e}")

class MS901MPlotter:
    """MS901M传感器数据实时绘图器"""
    
    def __init__(self, monitor):
        self.monitor = monitor
        self.fig, self.axes = plt.subplots(2, 2, figsize=(12, 8))
        self.fig.suptitle('MS901M加速度传感器实时数据')
        
        # 设置子图
        self.axes[0, 0].set_title('X轴加速度')
        self.axes[0, 0].set_ylabel('加速度 (mg)')
        self.axes[0, 1].set_title('Y轴加速度')
        self.axes[0, 1].set_ylabel('加速度 (mg)')
        self.axes[1, 0].set_title('Z轴加速度')
        self.axes[1, 0].set_ylabel('加速度 (mg)')
        self.axes[1, 0].set_xlabel('时间 (秒)')
        self.axes[1, 1].set_title('三轴合成')
        self.axes[1, 1].set_ylabel('加速度 (mg)')
        self.axes[1, 1].set_xlabel('时间 (秒)')
        
        # 初始化线条
        self.line_x, = self.axes[0, 0].plot([], [], 'r-', label='X轴')
        self.line_y, = self.axes[0, 1].plot([], [], 'g-', label='Y轴')
        self.line_z, = self.axes[1, 0].plot([], [], 'b-', label='Z轴')
        self.line_x2, = self.axes[1, 1].plot([], [], 'r-', label='X轴')
        self.line_y2, = self.axes[1, 1].plot([], [], 'g-', label='Y轴')
        self.line_z2, = self.axes[1, 1].plot([], [], 'b-', label='Z轴')
        
        self.axes[1, 1].legend()
        
        # 数据读取线程
        self.data_thread = None
        
    def update_plot(self, frame):
        """更新绘图"""
        with self.monitor.data_lock:
            if not self.monitor.timestamps:
                return self.line_x, self.line_y, self.line_z, self.line_x2, self.line_y2, self.line_z2
            
            # 转换时间为相对时间
            start_time = self.monitor.timestamps[0] if self.monitor.timestamps else 0
            times = [t - start_time for t in self.monitor.timestamps]
            
            # 更新数据
            self.line_x.set_data(times, list(self.monitor.accel_x))
            self.line_y.set_data(times, list(self.monitor.accel_y))
            self.line_z.set_data(times, list(self.monitor.accel_z))
            self.line_x2.set_data(times, list(self.monitor.accel_x))
            self.line_y2.set_data(times, list(self.monitor.accel_y))
            self.line_z2.set_data(times, list(self.monitor.accel_z))
            
            # 自动调整坐标轴
            for ax in self.axes.flat:
                ax.relim()
                ax.autoscale_view()
        
        return self.line_x, self.line_y, self.line_z, self.line_x2, self.line_y2, self.line_z2
    
    def data_reader_thread(self):
        """数据读取线程"""
        while self.monitor.running:
            data = self.monitor.read_sensor_data()
            if data:
                x, y, z = data
                self.monitor.update_data(x, y, z)
            time.sleep(0.05)  # 20Hz采样
    
    def start_realtime_plot(self):
        """开始实时绘图"""
        print("启动实时数据绘图...")
        print("关闭窗口停止监控")
        
        self.monitor.running = True
        self.monitor.start_time = time.time()
        
        # 启动数据读取线程
        self.data_thread = threading.Thread(target=self.data_reader_thread)
        self.data_thread.daemon = True
        self.data_thread.start()
        
        # 启动动画
        ani = animation.FuncAnimation(self.fig, self.update_plot, interval=100, blit=False)
        
        try:
            plt.show()
        except KeyboardInterrupt:
            pass
        finally:
            self.monitor.running = False
            self.monitor.print_statistics()

def main():
    parser = argparse.ArgumentParser(description='MS901M加速度传感器数据监控工具')
    parser.add_argument('-d', '--device', default='/dev/sensor_accel_ms901m',
                       help='传感器设备路径 (默认: /dev/sensor_accel_ms901m)')
    parser.add_argument('-t', '--time', type=float,
                       help='监控时长 (秒)')
    parser.add_argument('-i', '--interval', type=float, default=0.1,
                       help='采样间隔 (秒, 默认: 0.1)')
    parser.add_argument('-s', '--save', 
                       help='保存数据到CSV文件')
    parser.add_argument('-p', '--plot', action='store_true',
                       help='显示实时图表 (需要matplotlib)')
    parser.add_argument('--max-samples', type=int, default=1000,
                       help='最大样本数 (默认: 1000)')
    
    args = parser.parse_args()
    
    # 检查设备文件
    if not os.path.exists(args.device):
        print(f"错误: 设备文件不存在: {args.device}")
        print("请检查:")
        print("1. 驱动是否已加载")
        print("2. 设备节点是否正确创建")
        sys.exit(1)
    
    # 创建监控器
    monitor = MS901MMonitor(args.device, args.max_samples)
    
    # 设置信号处理
    def signal_handler(sig, frame):
        monitor.running = False
        print("\n监控已停止")
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    
    try:
        if args.plot and HAS_MATPLOTLIB:
            # 实时绘图模式
            plotter = MS901MPlotter(monitor)
            plotter.start_realtime_plot()
        else:
            # 控制台监控模式
            if args.plot and not HAS_MATPLOTLIB:
                print("警告: matplotlib未安装，使用控制台模式")
            
            monitor.monitor_console(args.interval, args.time)
        
        # 保存数据
        if args.save:
            monitor.save_data_to_file(args.save)
            
    except Exception as e:
        print(f"监控过程中发生错误: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
