root {
    module = "sensor";
    template sensor_controller {
        match_attr = "sensor_controller";
        template sensor_chip {
            match_attr = "sensor_chip";
            template sensor_device {
                match_attr = "sensor_device";
                deviceName = "";
                deviceId = 0;
                busType = 0;
                busNum = 0;
                addr = 0;
                irqNum = 0;
                irqShare = 0;
                regSize = 0;
                sensorTypeId = 0;
                sensorId = 0;
                vendorName = "";
                chipName = "";
                chipId = 0;
                chipVersion = 0;
                powerOnDelay = 0;
                powerOffDelay = 0;
                minDelay = 0;
                maxDelay = 0;
                fifoMaxEventCount = 0;
                maxRange = 0;
                resolution = 0;
                power = 0;
            }
        }
    }

    accelerometer_controller :: sensor_controller {
        accelerometer_chip :: sensor_chip {
            bma250_device :: sensor_device {
                deviceName = "accelerometer";
                deviceId = 0;
                busType = 1;  /* I2C */
                busNum = 1;
                addr = 0x18;
                irqNum = 0;
                irqShare = 0;
                regSize = 1;
                sensorTypeId = 1;  /* 加速度传感器 */
                sensorId = 1;
                vendorName = "Bosch";
                chipName = "BMA250";
                chipId = 0xF9;
                chipVersion = 0;
                powerOnDelay = 10;
                powerOffDelay = 10;
                minDelay = 10000000;  /* 10ms */
                maxDelay = 1000000000;  /* 1s */
                fifoMaxEventCount = 32;
                maxRange = 16;  /* 16G */
                resolution = 0.001;  /* 1mg */
                power = 0.001;  /* 1mW */
            }
        }
    }
} 