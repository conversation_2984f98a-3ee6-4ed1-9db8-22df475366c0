# MS901M加速度传感器测试程序 Makefile

# 编译器设置
CC = gcc
CROSS_COMPILE ?= 
TARGET_CC = $(CROSS_COMPILE)$(CC)

# 编译选项
CFLAGS = -Wall -Wextra -O2 -std=c99
LDFLAGS = -lm

# 目标文件
TARGET = ms901m_accel_test
SOURCE = ms901m_accel_test.c

# 默认目标
all: $(TARGET)

# 编译目标
$(TARGET): $(SOURCE)
	$(TARGET_CC) $(CFLAGS) -o $@ $< $(LDFLAGS)
	@echo "编译完成: $(TARGET)"

# 交叉编译目标 (ARM64)
arm64: CROSS_COMPILE = aarch64-linux-gnu-
arm64: $(TARGET)
	@echo "ARM64交叉编译完成"

# 交叉编译目标 (ARM32)
arm32: CROSS_COMPILE = arm-linux-gnueabihf-
arm32: $(TARGET)
	@echo "ARM32交叉编译完成"

# 安装到目标设备
install: $(TARGET)
	@if [ -z "$(DEVICE_IP)" ]; then \
		echo "错误: 请设置DEVICE_IP环境变量"; \
		echo "示例: make install DEVICE_IP=*************"; \
		exit 1; \
	fi
	@echo "安装到设备: $(DEVICE_IP)"
	scp $(TARGET) root@$(DEVICE_IP):/system/bin/
	@echo "安装完成"

# 在目标设备上运行测试
test: install
	@echo "在设备上运行测试..."
	ssh root@$(DEVICE_IP) "/system/bin/$(TARGET)"

# 清理
clean:
	rm -f $(TARGET)
	@echo "清理完成"

# 显示帮助
help:
	@echo "MS901M加速度传感器测试程序编译选项:"
	@echo ""
	@echo "目标:"
	@echo "  all        - 编译本地版本 (默认)"
	@echo "  arm64      - 交叉编译ARM64版本"
	@echo "  arm32      - 交叉编译ARM32版本"
	@echo "  install    - 安装到目标设备 (需要DEVICE_IP)"
	@echo "  test       - 安装并运行测试"
	@echo "  clean      - 清理编译文件"
	@echo "  help       - 显示此帮助信息"
	@echo ""
	@echo "示例:"
	@echo "  make                              # 编译本地版本"
	@echo "  make arm64                        # 交叉编译ARM64版本"
	@echo "  make install DEVICE_IP=*************  # 安装到设备"
	@echo "  make test DEVICE_IP=*************     # 安装并测试"
	@echo ""
	@echo "环境变量:"
	@echo "  CROSS_COMPILE  - 交叉编译工具链前缀"
	@echo "  DEVICE_IP      - 目标设备IP地址"

.PHONY: all arm64 arm32 install test clean help
