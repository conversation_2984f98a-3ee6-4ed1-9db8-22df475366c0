/*
 * Copyright (c) 2024 Huawei Device Co., Ltd.
 *
 * HDF is dual licensed: you can use it either under the terms of
 * the GPL, or the BSD license, at your option.
 * See the LICENSE file in the root of this repository for complete details.
 */

#include "sensor_accelerometer_driver.h"
#include "hdf_base.h"
#include "hdf_log.h"
#include "osal_mem.h"
#include "sensor_config_parser.h"

#define HDF_LOG_TAG bma250_accelerometer

/* BMA250寄存器定义 */
#define BMA250_CHIP_ID_REG          0x00
#define BMA250_CHIP_ID              0xF9
#define BMA250_X_AXIS_LSB_REG       0x02
#define BMA250_X_AXIS_MSB_REG       0x03
#define BMA250_Y_AXIS_LSB_REG       0x04
#define BMA250_Y_AXIS_MSB_REG       0x05
#define BMA250_Z_AXIS_LSB_REG       0x06
#define BMA250_Z_AXIS_MSB_REG       0x07
#define BMA250_RANGE_SELECT_REG     0x0F
#define BMA250_BANDWIDTH_REG        0x10
#define BMA250_POWER_CTRL_REG       0x11
#define BMA250_POWER_CTRL_MASK      0x01

/* BMA250量程定义 */
#define BMA250_RANGE_2G             0x03
#define BMA250_RANGE_4G             0x05
#define BMA250_RANGE_8G             0x08
#define BMA250_RANGE_16G            0x0C

/* BMA250私有数据结构 */
struct Bma250PrivateData {
    struct SensorCfgData *cfg;
    int32_t range;
    float sensitivity;
};

/* 读取寄存器 */
static int32_t Bma250ReadReg(struct SensorCfgData *cfg, uint8_t reg, uint8_t *data)
{
    if (cfg == NULL || data == NULL) {
        HDF_LOGE("%s: cfg or data is null", __func__);
        return HDF_ERR_INVALID_PARAM;
    }

    return ReadSensor(cfg, reg, data, sizeof(uint8_t));
}

/* 写入寄存器 */
static int32_t Bma250WriteReg(struct SensorCfgData *cfg, uint8_t reg, uint8_t data)
{
    if (cfg == NULL) {
        HDF_LOGE("%s: cfg is null", __func__);
        return HDF_ERR_INVALID_PARAM;
    }

    return WriteSensor(cfg, reg, &data, sizeof(uint8_t));
}

/* 初始化BMA250 */
static int32_t Bma250Init(struct SensorCfgData *cfg)
{
    uint8_t chipId = 0;
    struct Bma250PrivateData *priv = NULL;
    int32_t ret;

    if (cfg == NULL) {
        HDF_LOGE("%s: cfg is null", __func__);
        return HDF_ERR_INVALID_PARAM;
    }

    /* 分配私有数据 */
    priv = (struct Bma250PrivateData *)OsalMemCalloc(sizeof(*priv));
    if (priv == NULL) {
        HDF_LOGE("%s: malloc priv failed", __func__);
        return HDF_ERR_MALLOC_FAIL;
    }
    cfg->priv = priv;
    priv->cfg = cfg;

    /* 读取芯片ID */
    ret = Bma250ReadReg(cfg, BMA250_CHIP_ID_REG, &chipId);
    if (ret != HDF_SUCCESS) {
        HDF_LOGE("%s: read chip id failed", __func__);
        goto err_out;
    }

    if (chipId != BMA250_CHIP_ID) {
        HDF_LOGE("%s: chip id 0x%x is not match", __func__, chipId);
        ret = HDF_ERR_DEVICE_BUSY;
        goto err_out;
    }

    /* 设置默认量程为2G */
    ret = Bma250WriteReg(cfg, BMA250_RANGE_SELECT_REG, BMA250_RANGE_2G);
    if (ret != HDF_SUCCESS) {
        HDF_LOGE("%s: set range failed", __func__);
        goto err_out;
    }
    priv->range = BMA250_RANGE_2G;
    priv->sensitivity = 256.0f;  /* 2G量程下的灵敏度 */

    /* 使能传感器 */
    ret = Bma250WriteReg(cfg, BMA250_POWER_CTRL_REG, BMA250_POWER_CTRL_MASK);
    if (ret != HDF_SUCCESS) {
        HDF_LOGE("%s: enable sensor failed", __func__);
        goto err_out;
    }

    return HDF_SUCCESS;

err_out:
    if (priv != NULL) {
        OsalMemFree(priv);
        cfg->priv = NULL;
    }
    return ret;
}

/* 读取加速度数据 */
static int32_t Bma250ReadData(struct SensorCfgData *cfg)
{
    uint8_t data[6];
    struct Bma250PrivateData *priv = NULL;
    struct AccelerometerData *accelData = NULL;
    int32_t ret;

    if (cfg == NULL || cfg->priv == NULL) {
        HDF_LOGE("%s: cfg or priv is null", __func__);
        return HDF_ERR_INVALID_PARAM;
    }

    priv = (struct Bma250PrivateData *)cfg->priv;
    accelData = (struct AccelerometerData *)cfg->data;

    /* 读取三轴数据 */
    ret = ReadSensor(cfg, BMA250_X_AXIS_LSB_REG, data, sizeof(data));
    if (ret != HDF_SUCCESS) {
        HDF_LOGE("%s: read data failed", __func__);
        return ret;
    }

    /* 转换数据 */
    accelData->x = (int16_t)((data[1] << 8) | data[0]) / priv->sensitivity;
    accelData->y = (int16_t)((data[3] << 8) | data[2]) / priv->sensitivity;
    accelData->z = (int16_t)((data[5] << 8) | data[4]) / priv->sensitivity;

    return HDF_SUCCESS;
}

/* 设置量程 */
static int32_t Bma250SetRange(struct SensorCfgData *cfg, int32_t range)
{
    struct Bma250PrivateData *priv = NULL;
    int32_t ret;

    if (cfg == NULL || cfg->priv == NULL) {
        HDF_LOGE("%s: cfg or priv is null", __func__);
        return HDF_ERR_INVALID_PARAM;
    }

    priv = (struct Bma250PrivateData *)cfg->priv;

    /* 设置量程 */
    ret = Bma250WriteReg(cfg, BMA250_RANGE_SELECT_REG, range);
    if (ret != HDF_SUCCESS) {
        HDF_LOGE("%s: set range failed", __func__);
        return ret;
    }

    /* 更新灵敏度 */
    switch (range) {
        case BMA250_RANGE_2G:
            priv->sensitivity = 256.0f;
            break;
        case BMA250_RANGE_4G:
            priv->sensitivity = 128.0f;
            break;
        case BMA250_RANGE_8G:
            priv->sensitivity = 64.0f;
            break;
        case BMA250_RANGE_16G:
            priv->sensitivity = 32.0f;
            break;
        default:
            HDF_LOGE("%s: invalid range %d", __func__, range);
            return HDF_ERR_INVALID_PARAM;
    }

    priv->range = range;
    return HDF_SUCCESS;
}

/* 设置工作模式 */
static int32_t Bma250SetMode(struct SensorCfgData *cfg, int32_t mode)
{
    if (cfg == NULL) {
        HDF_LOGE("%s: cfg is null", __func__);
        return HDF_ERR_INVALID_PARAM;
    }

    /* 根据模式设置带宽等参数 */
    return HDF_SUCCESS;
}

/* BMA250操作函数集 */
static struct AccelerometerOpsCall g_bma250Ops = {
    .Init = Bma250Init,
    .ReadData = Bma250ReadData,
    .SetRange = Bma250SetRange,
    .SetMode = Bma250SetMode,
};

/* 注册BMA250驱动 */
int32_t Bma250Register(void)
{
    return AccelerometerRegisterChipOps(&g_bma250Ops);
} 