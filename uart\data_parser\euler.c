#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <string.h>
#include <malloc.h>
#include "euler.h"

#include "../common/common.h"

#define EULER_PAYLOAD_SIZE 6 // bytes


// Function to convert euler_data to JSON-like string (simplified)
char *euler_data_to_json(euler_data *data) {
    char *json = (char *) malloc(256 * sizeof(char));
    if (json == NULL) {
        return NULL; // Memory allocation failed  
    }
    snprintf(json, 256, "{\"source\":\"euler\",\"data\":{\"roll\":%f,\"pitch\":%f,\"yaw\":%f}}",
             data->roll, data->pitch, data->yaw);
    return json;
}

// Function to decode Euler payload  
euler_data *decode_euler(uint8_t *payload) {
    if (payload == NULL || malloc_usable_size(payload) != EULER_PAYLOAD_SIZE) {
        printf("[%s, %d]\n", __FUNCTION__, __LINE__);
        return NULL; // incorrect payload  
    }

    euler_data *eulerData = (euler_data *) malloc(sizeof(euler_data));
    if (eulerData == NULL) {
        return NULL; // Memory allocation failed  
    }

    uint8_t rollL = payload[0];
    uint8_t rollH = payload[1];
    uint8_t pitchL = payload[2];
    uint8_t pitchH = payload[3];
    uint8_t yawL = payload[4];
    uint8_t yawH = payload[5];

    int16_t roll = (int16_t)((uint16_t) rollH << 8 | (uint16_t) rollL);
    int16_t pitch = (int16_t)((uint16_t) pitchH << 8 | (uint16_t) pitchL);
    int16_t yaw = (int16_t)((uint16_t) yawH << 8 | (uint16_t) yawL);

    eulerData->roll = (double) roll / 32768.0 * 180.0;
    eulerData->pitch = (double) pitch / 32768.0 * 180.0;
    eulerData->yaw = (double) yaw / 32768.0 * 180.0;

    return eulerData;
}

// 欧拉角(Euler angles)
int process_euler_data(uint8_t *payload) {
    //printf("[%s, %d] payload size = %d \n", __FUNCTION__, __LINE__, malloc_usable_size(payload));
    if (payload == NULL) {
        printf("Payload is NULL\n");
    }
    //print_buf_with_hex(payload, EULER_PAYLOAD_SIZE);

    euler_data *data = decode_euler(payload);
    if (data != NULL) {
        char *json = euler_data_to_json(data);
        if (json != NULL) {
            printf("JSON: %s\n", json);
            free(json);
        }
        free(data);
    } else {
        printf("Failed to decode Euler data\n");
    }

    return 0;
}