/*
 * Copyright (c) 2024 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <signal.h>
#include <time.h>
#include <fcntl.h>
#include <errno.h>
#include <math.h>
#include "hilog/log.h"
#include "securec.h"

/* OpenHarmony日志标签 */
#undef LOG_DOMAIN
#undef LOG_TAG
#define LOG_DOMAIN 0xD002D01
#define LOG_TAG "MS901M_MONITOR"

/* 配置参数 */
#define SENSOR_DEVICE_PATH          "/dev/sensor_accel_ms901m"
#define DEFAULT_MONITOR_DURATION    30      /* 默认监控30秒 */
#define DEFAULT_SAMPLE_INTERVAL     100     /* 默认采样间隔100ms */
#define MAX_SAMPLES                 1000    /* 最大样本数 */

/* 传感器数据结构 */
struct SensorData {
    int32_t x;          /* X轴加速度 (mg) */
    int32_t y;          /* Y轴加速度 (mg) */
    int32_t z;          /* Z轴加速度 (mg) */
    uint64_t timestamp; /* 时间戳 (ns) */
};

/* 统计信息结构 */
struct MonitorStats {
    int32_t totalSamples;
    int32_t validSamples;
    int32_t errorSamples;
    double avgX, avgY, avgZ;
    int32_t minX, maxX;
    int32_t minY, maxY;
    int32_t minZ, maxZ;
    uint64_t startTime;
    uint64_t endTime;
    double magnitude;
};

static volatile int g_monitorRunning = 1;
static struct MonitorStats g_stats;

/* 信号处理函数 */
void SignalHandler(int sig)
{
    HILOG_INFO(LOG_CORE, "收到信号 %{public}d，停止监控...", sig);
    printf("\n收到信号 %d，停止监控...\n", sig);
    g_monitorRunning = 0;
}

/* 获取当前时间戳 (纳秒) */
uint64_t GetCurrentTimeNs(void)
{
    struct timespec ts;
    if (clock_gettime(CLOCK_MONOTONIC, &ts) != 0) {
        return 0;
    }
    return (uint64_t)ts.tv_sec * 1000000000ULL + (uint64_t)ts.tv_nsec;
}

/* 初始化统计信息 */
void InitStats(struct MonitorStats *stats)
{
    (void)memset_s(stats, sizeof(struct MonitorStats), 0, sizeof(struct MonitorStats));
    stats->minX = stats->minY = stats->minZ = INT32_MAX;
    stats->maxX = stats->maxY = stats->maxZ = INT32_MIN;
    stats->startTime = GetCurrentTimeNs();
}

/* 更新统计信息 */
void UpdateStats(struct MonitorStats *stats, const struct SensorData *data)
{
    stats->totalSamples++;
    
    if (data->x == 0 && data->y == 0 && data->z == 0) {
        stats->errorSamples++;
        return;
    }
    
    stats->validSamples++;
    
    /* 更新平均值 */
    stats->avgX = (stats->avgX * (stats->validSamples - 1) + data->x) / stats->validSamples;
    stats->avgY = (stats->avgY * (stats->validSamples - 1) + data->y) / stats->validSamples;
    stats->avgZ = (stats->avgZ * (stats->validSamples - 1) + data->z) / stats->validSamples;
    
    /* 更新最值 */
    if (data->x < stats->minX) stats->minX = data->x;
    if (data->x > stats->maxX) stats->maxX = data->x;
    if (data->y < stats->minY) stats->minY = data->y;
    if (data->y > stats->maxY) stats->maxY = data->y;
    if (data->z < stats->minZ) stats->minZ = data->z;
    if (data->z > stats->maxZ) stats->maxZ = data->z;
    
    /* 计算重力加速度模长 */
    stats->magnitude = sqrt(stats->avgX * stats->avgX + 
                           stats->avgY * stats->avgY + 
                           stats->avgZ * stats->avgZ);
}

/* 打印统计信息 */
void PrintStats(const struct MonitorStats *stats)
{
    double testDuration = (stats->endTime - stats->startTime) / 1000000000.0;
    double sampleRate = stats->totalSamples / testDuration;
    
    printf("\n=== MS901M传感器监控统计 ===\n");
    printf("监控时长: %.2f 秒\n", testDuration);
    printf("总采样数: %d\n", stats->totalSamples);
    printf("有效采样: %d\n", stats->validSamples);
    printf("错误采样: %d\n", stats->errorSamples);
    printf("采样率: %.2f Hz\n", sampleRate);
    printf("成功率: %.2f%%\n", (double)stats->validSamples / stats->totalSamples * 100);
    
    if (stats->validSamples > 0) {
        printf("\n--- 加速度数据统计 (mg) ---\n");
        printf("X轴: 平均=%.2f, 范围=[%d, %d]\n", stats->avgX, stats->minX, stats->maxX);
        printf("Y轴: 平均=%.2f, 范围=[%d, %d]\n", stats->avgY, stats->minY, stats->maxY);
        printf("Z轴: 平均=%.2f, 范围=[%d, %d]\n", stats->avgZ, stats->minZ, stats->maxZ);
        printf("重力加速度模长: %.2f mg (理论值: 1000mg)\n", stats->magnitude);
        
        /* 数据质量评估 */
        double zError = fabs(stats->avgZ - 1000.0);
        if (zError < 50.0) {
            printf("数据质量: 优秀 (Z轴误差 < 50mg)\n");
        } else if (zError < 100.0) {
            printf("数据质量: 良好 (Z轴误差 < 100mg)\n");
        } else {
            printf("数据质量: 需要校准 (Z轴误差 > 100mg)\n");
        }
    }
    
    /* 记录到HiLog */
    HILOG_INFO(LOG_CORE, "监控完成: 样本=%{public}d, 成功率=%.2f%%, 重力模长=%.2f mg", 
               stats->totalSamples, (double)stats->validSamples / stats->totalSamples * 100, 
               stats->magnitude);
}

/* 读取传感器数据 */
int ReadSensorData(int fd, struct SensorData *data)
{
    int32_t rawData[3];
    ssize_t bytesRead;
    
    /* 读取原始数据 */
    bytesRead = read(fd, rawData, sizeof(rawData));
    if (bytesRead != sizeof(rawData)) {
        if (bytesRead < 0) {
            HILOG_ERROR(LOG_CORE, "读取传感器数据失败: %{public}s", strerror(errno));
        }
        return -1;
    }
    
    /* 转换数据格式 */
    data->x = rawData[0];
    data->y = rawData[1];
    data->z = rawData[2];
    data->timestamp = GetCurrentTimeNs();
    
    return 0;
}

/* 实时监控模式 */
int MonitorRealtime(int duration, int interval)
{
    int fd;
    struct SensorData data;
    int sampleCount = 0;
    
    printf("=== MS901M传感器实时监控 ===\n");
    printf("监控时长: %d 秒\n", duration);
    printf("采样间隔: %d ms\n", interval);
    printf("按 Ctrl+C 提前停止\n\n");
    
    /* 打开传感器设备 */
    fd = open(SENSOR_DEVICE_PATH, O_RDONLY);
    if (fd < 0) {
        printf("打开传感器设备失败: %s\n", strerror(errno));
        HILOG_ERROR(LOG_CORE, "打开传感器设备失败: %{public}s", strerror(errno));
        return -1;
    }
    
    /* 初始化统计信息 */
    InitStats(&g_stats);
    
    /* 开始监控 */
    while (g_monitorRunning && sampleCount < MAX_SAMPLES) {
        if (ReadSensorData(fd, &data) == 0) {
            UpdateStats(&g_stats, &data);
            
            /* 每10个样本打印一次进度 */
            if (sampleCount % 10 == 0) {
                printf("\r样本 %d: X=%6d, Y=%6d, Z=%6d mg", 
                       sampleCount + 1, data.x, data.y, data.z);
                fflush(stdout);
            }
            
            sampleCount++;
        }
        
        /* 检查监控时长 */
        uint64_t currentTime = GetCurrentTimeNs();
        if ((currentTime - g_stats.startTime) / 1000000000ULL >= (uint64_t)duration) {
            break;
        }
        
        /* 等待下次采样 */
        usleep(interval * 1000);
    }
    
    g_stats.endTime = GetCurrentTimeNs();
    close(fd);
    
    printf("\n\n监控完成\n");
    PrintStats(&g_stats);
    
    return 0;
}

/* 显示帮助信息 */
void ShowHelp(const char *progName)
{
    printf("MS901M加速度传感器数据监控工具\n");
    printf("用法: %s [选项]\n", progName);
    printf("选项:\n");
    printf("  -h          显示帮助信息\n");
    printf("  -t <秒>     监控时长 (默认: %d秒)\n", DEFAULT_MONITOR_DURATION);
    printf("  -i <毫秒>   采样间隔 (默认: %dms)\n", DEFAULT_SAMPLE_INTERVAL);
    printf("\n");
    printf("示例:\n");
    printf("  %s              # 默认监控30秒\n", progName);
    printf("  %s -t 60        # 监控60秒\n", progName);
    printf("  %s -t 10 -i 50  # 监控10秒，50ms间隔\n", progName);
}

int main(int argc, char *argv[])
{
    int duration = DEFAULT_MONITOR_DURATION;
    int interval = DEFAULT_SAMPLE_INTERVAL;
    int opt;
    
    /* 注册信号处理函数 */
    signal(SIGINT, SignalHandler);
    signal(SIGTERM, SignalHandler);
    
    /* 解析命令行参数 */
    while ((opt = getopt(argc, argv, "ht:i:")) != -1) {
        switch (opt) {
            case 'h':
                ShowHelp(argv[0]);
                return 0;
            case 't':
                duration = atoi(optarg);
                if (duration <= 0) {
                    printf("错误: 监控时长必须大于0\n");
                    return 1;
                }
                break;
            case 'i':
                interval = atoi(optarg);
                if (interval <= 0) {
                    printf("错误: 采样间隔必须大于0\n");
                    return 1;
                }
                break;
            default:
                ShowHelp(argv[0]);
                return 1;
        }
    }
    
    printf("MS901M传感器数据监控工具\n");
    printf("========================\n");
    
    HILOG_INFO(LOG_CORE, "开始MS901M传感器监控，时长=%{public}d秒，间隔=%{public}dms", 
               duration, interval);
    
    /* 开始监控 */
    int ret = MonitorRealtime(duration, interval);
    
    HILOG_INFO(LOG_CORE, "MS901M传感器监控结束，返回值=%{public}d", ret);
    
    return ret;
}
