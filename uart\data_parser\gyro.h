#ifndef GYRO_H
#define GYRO_H

#include <stdlib.h>
// 定义 gyro_data 结构体
typedef struct {
    double acc_x;
    double acc_y;
    double acc_z;
    double gyro_x;
    double gyro_y;
    double gyro_z;
} gyro_data;

// 定义 device_gyro_range 结构体
typedef struct {
    int accelRange;
    int gyroRange;
} device_gyro_range;

// 声明函数原型
char *gyro_data_to_json(gyro_data *data);

device_gyro_range get_device_gyro_range();

gyro_data *decode_gyro(uint8_t *payload, device_gyro_range gyro_range);

int process_gyro_data(uint8_t *payload);


#endif