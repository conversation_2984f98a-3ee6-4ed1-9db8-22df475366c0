#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <string.h>
#include <malloc.h>
#include "cJSON.h"
#include "../common/common.h"
#include "magnetic.h"

#define MAGNETIC_PAYLOAD_SIZE 8



// 将磁数据转换为 JSON 字符串  
char *magnetic_data_to_json(magnetic_data *data) {
    cJSON *json = cJSON_CreateObject();
    if (json == NULL) return NULL;

    cJSON_AddStringToObject(json, "source", "magnetic");

    cJSON *data_json = cJSON_CreateObject();
    if (data_json == NULL) {
        cJSON_Delete(json);
        return NULL;
    }

    cJSON_AddNumberToObject(data_json, "magnet_x", data->magnet_x);
    cJSON_AddNumberToObject(data_json, "magnet_y", data->magnet_y);
    cJSON_AddNumberToObject(data_json, "magnet_z", data->magnet_z);
    cJSON_AddNumberToObject(data_json, "temperature", data->temperature);

    cJSON_AddItemToObject(json, "data", data_json);

    char *json_string = cJSON_Print(json);
    cJSON_Delete(json);

    return json_string;
}

// 从二进制数据解码磁数据  
magnetic_data *decode_magnetic(uint8_t *payload) {
    if (payload == NULL || malloc_usable_size(payload) != MAGNETIC_PAYLOAD_SIZE) {
        printf("[%s, %d]\n", __FUNCTION__, __LINE__);
        return NULL;
    }

    magnetic_data *data = (magnetic_data *) malloc(sizeof(magnetic_data));
    if (data == NULL) return NULL;

    uint16_t mXH = payload[1] << 8 | payload[0];
    uint16_t mYH = payload[3] << 8 | payload[2];
    uint16_t mZH = payload[5] << 8 | payload[4];
    uint16_t tH = payload[7] << 8 | payload[6];

    data->magnet_x = (double) mXH / 1000.0;
    data->magnet_y = (double) mYH / 1000.0;
    data->magnet_z = (double) mZH / 1000.0;
    data->temperature = (double) tH / 100.0;

    return data;
}


int process_magnetic_data(uint8_t *payload) {
    //printf("[%s, %d] payload size = %d \n", __FUNCTION__, __LINE__, malloc_usable_size(payload));
    if (payload == NULL) {
        printf("Payload is NULL\n");
    }
    //print_buf_with_hex(payload, MAGNETIC_PAYLOAD_SIZE);

    magnetic_data *data = decode_magnetic(payload);
    if (data != NULL) {
        char *json_string = magnetic_data_to_json(data);
        if (json_string != NULL) {
            printf("JSON: %s\n", json_string);
            free(json_string);
        }
        free(data);
    }

    return 0;
}