#!/bin/bash

# MS901M传感器测试工具集成到OpenHarmony脚本
# 用于将测试工具集成到OH构建系统中

set -e

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

# 检查是否在OpenHarmony根目录
check_oh_root() {
    if [ ! -f "build.sh" ] || [ ! -d "build" ]; then
        print_error "请在OpenHarmony根目录下运行此脚本"
        print_info "当前目录: $(pwd)"
        print_info "应该包含: build.sh, build/, drivers/ 等目录"
        exit 1
    fi
    
    print_success "检测到OpenHarmony根目录"
}

# 检查测试工具文件是否存在
check_test_files() {
    print_header "检查测试工具文件"
    
    local test_dir="drivers/peripheral/sensor/chipset/accel/test"
    local required_files=(
        "BUILD.gn"
        "bundle.json"
        "ms901m_accel_test.c"
        "ms901m_data_monitor_simple.c"
    )
    
    if [ ! -d "$test_dir" ]; then
        print_error "测试工具目录不存在: $test_dir"
        exit 1
    fi
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$test_dir/$file" ]; then
            print_error "缺少文件: $test_dir/$file"
            exit 1
        else
            print_success "找到文件: $file"
        fi
    done
}

# 检查依赖组件
check_dependencies() {
    print_header "检查依赖组件"
    
    local deps=(
        "base/hiviewdfx/hilog"
        "utils/native/base"
        "third_party/bounds_checking_function"
    )
    
    for dep in "${deps[@]}"; do
        if [ -d "$dep" ]; then
            print_success "依赖存在: $dep"
        else
            print_warning "依赖缺失: $dep"
        fi
    done
}

# 添加到产品配置
add_to_product_config() {
    print_header "添加到产品配置"
    
    local product_config="vendor/hihope/rk3568/config.json"
    
    if [ ! -f "$product_config" ]; then
        print_warning "产品配置文件不存在: $product_config"
        print_info "请手动添加测试工具到产品配置中"
        return
    fi
    
    # 检查是否已经添加
    if grep -q "ms901m_test_tools" "$product_config"; then
        print_info "测试工具已在产品配置中"
        return
    fi
    
    print_info "建议手动添加以下配置到 $product_config:"
    cat << EOF

在 "subsystem": "hdf" 的 components 中添加:
{
  "component": "drivers_peripheral_sensor",
  "features": [
    "ms901m_sensor_test_support = true"
  ]
}

EOF
}

# 编译测试
compile_test() {
    print_header "编译测试"
    
    print_info "开始编译MS901M测试工具..."
    
    # 编译测试工具
    if ./build.sh --product-name rk3568 --build-target ms901m_test_tools; then
        print_success "测试工具编译成功"
    else
        print_error "测试工具编译失败"
        print_info "请检查:"
        print_info "1. 依赖组件是否存在"
        print_info "2. BUILD.gn配置是否正确"
        print_info "3. 源码是否有语法错误"
        return 1
    fi
    
    # 检查编译输出
    local out_dir="out/rk3568/packages/phone/system/bin"
    if [ -f "$out_dir/ms901m_accel_test" ] && [ -f "$out_dir/ms901m_data_monitor" ]; then
        print_success "编译输出文件存在"
        ls -la "$out_dir"/ms901m*
    else
        print_warning "编译输出文件不存在，请检查编译过程"
    fi
}

# 生成使用说明
generate_usage() {
    print_header "生成使用说明"
    
    cat << EOF

${GREEN}=== MS901M测试工具集成完成 ===${NC}

${BLUE}编译命令:${NC}
  # 编译特定工具
  ./build.sh --product-name rk3568 --build-target ms901m_accel_test
  ./build.sh --product-name rk3568 --build-target ms901m_data_monitor
  
  # 编译所有测试工具
  ./build.sh --product-name rk3568 --build-target ms901m_test_tools
  
  # 编译整个系统
  ./build.sh --product-name rk3568

${BLUE}部署到设备:${NC}
  # 烧录完整镜像
  hdc file send out/rk3568/packages/phone/images/system.img /path/to/device
  
  # 或单独推送可执行文件
  hdc file send out/rk3568/packages/phone/system/bin/ms901m_accel_test /system/bin/
  hdc file send out/rk3568/packages/phone/system/bin/ms901m_data_monitor /system/bin/

${BLUE}在设备上运行:${NC}
  # 连接设备
  hdc shell
  
  # 运行测试
  ms901m_accel_test
  ms901m_data_monitor
  
  # 查看帮助
  ms901m_accel_test -h
  ms901m_data_monitor -h

${BLUE}查看日志:${NC}
  # HiLog日志
  hilog | grep MS901M
  
  # 内核日志
  dmesg | grep ms901m

${BLUE}文档位置:${NC}
  drivers/peripheral/sensor/chipset/accel/test/README_OPENHARMONY_zh.md

EOF
}

# 主函数
main() {
    print_header "MS901M传感器测试工具集成到OpenHarmony"
    
    # 检查环境
    check_oh_root
    check_test_files
    check_dependencies
    
    # 配置集成
    add_to_product_config
    
    # 编译测试
    if [ "${1:-}" != "--no-compile" ]; then
        compile_test
    else
        print_info "跳过编译测试 (使用了 --no-compile 参数)"
    fi
    
    # 生成使用说明
    generate_usage
    
    print_success "集成完成!"
}

# 显示帮助
show_help() {
    cat << EOF
MS901M传感器测试工具集成脚本

用法: $0 [选项]

选项:
  -h, --help        显示帮助信息
  --no-compile      跳过编译测试

示例:
  $0                # 完整集成和编译测试
  $0 --no-compile   # 只检查环境，不编译

EOF
}

# 解析命令行参数
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
