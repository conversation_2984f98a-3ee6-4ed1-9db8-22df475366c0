/*
 * Copyright (c) 2024 Huawei Device Co., Ltd.
 *
 * HDF is dual licensed: you can use it either under the terms of
 * the GPL, or the BSD license, at your option.
 * See the LICENSE file in the root of this repository for complete details.
 */

#ifndef SENSOR_ACCELEROMETER_DRIVER_H
#define SENSOR_ACCELEROMETER_DRIVER_H

#include "hdf_workqueue.h"
#include "osal_timer.h"
#include "sensor_config_parser.h"
#include "sensor_platform_if.h"

/* 加速度传感器默认采样间隔(1000ms) */
#define ACCELEROMETER_DEFAULT_SAMPLING_1000_MS    **********

/* 加速度传感器数据结构 */
struct AccelerometerData {
    float x;  /* X轴加速度值 */
    float y;  /* Y轴加速度值 */
    float z;  /* Z轴加速度值 */
};

/* 加速度传感器操作回调函数结构 */
struct AccelerometerOpsCall {
    int32_t (*Init)(struct SensorCfgData *data);           /* 初始化函数 */
    int32_t (*ReadData)(struct SensorCfgData *data);       /* 读取数据函数 */
    int32_t (*SetRange)(struct SensorCfgData *data, int32_t range);  /* 设置量程函数 */
    int32_t (*SetMode)(struct SensorCfgData *data, int32_t mode);    /* 设置工作模式函数 */
};

/* 加速度传感器驱动数据结构 */
struct AccelerometerDrvData {
    struct IDeviceIoService ioService;      /* 设备IO服务 */
    struct HdfDeviceObject *device;         /* 设备对象 */
    HdfWorkQueue accelerometerWorkQueue;    /* 工作队列 */
    HdfWork accelerometerWork;              /* 工作项 */
    OsalTimer accelerometerTimer;           /* 定时器 */
    bool detectFlag;                        /* 检测标志 */
    bool enable;                            /* 使能标志 */
    int64_t interval;                       /* 采样间隔 */
    struct SensorCfgData *accelerometerCfg; /* 配置数据 */
    struct AccelerometerOpsCall ops;        /* 操作回调函数 */
};

/* 函数声明 */
int32_t AccelerometerRegisterChipOps(const struct AccelerometerOpsCall *ops);
struct SensorCfgData *AccelerometerCreateCfgData(const struct DeviceResourceNode *node);
void AccelerometerReleaseCfgData(struct SensorCfgData *accelerometerCfg);

#endif /* SENSOR_ACCELEROMETER_DRIVER_H */ 