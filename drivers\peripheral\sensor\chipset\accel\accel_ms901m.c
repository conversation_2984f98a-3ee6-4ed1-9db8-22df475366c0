/*
 * Copyright (c) 2024 Huawei Device Co., Ltd.
 *
 * HDF is dual licensed: you can use it either under the terms of
 * the GPL, or the BSD license, at your option.
 * See the LICENSE file in the root of this repository for complete details.
 */

#include "accel_ms901m.h"
#include <securec.h>
#include "osal_mem.h"
#include "osal_time.h"
#include "sensor_accel_driver.h"
#include "sensor_config_controller.h"
#include "sensor_device_manager.h"

#define HDF_LOG_TAG    khdf_sensor_accel_driver

static struct Ms901mDrvData *g_ms901mDrvData = NULL;

static struct Ms901mDrvData *Ms901mGetDrvData(void)
{
    return g_ms901mDrvData;
}

/* 解析MS901M加速度计数据 */
static int32_t Ms901mDecodeAccelData(uint8_t *payload, struct AccelData *accelData)
{
    CHECK_NULL_PTR_RETURN_VALUE(payload, HDF_ERR_INVALID_PARAM);
    CHECK_NULL_PTR_RETURN_VALUE(accelData, HDF_ERR_INVALID_PARAM);

    // MS901M GYRO帧格式：前6字节是加速度数据，后6字节是陀螺仪数据
    uint16_t accXRaw = (uint16_t)payload[1] << 8 | (uint16_t)payload[0];
    uint16_t accYRaw = (uint16_t)payload[3] << 8 | (uint16_t)payload[2];
    uint16_t accZRaw = (uint16_t)payload[5] << 8 | (uint16_t)payload[4];

    // 转换为加速度值，使用2G量程，16位精度
    accelData->x = (int16_t)accXRaw * MS901M_ACC_SENSITIVITY_2G;
    accelData->y = (int16_t)accYRaw * MS901M_ACC_SENSITIVITY_2G;
    accelData->z = (int16_t)accZRaw * MS901M_ACC_SENSITIVITY_2G;

    return HDF_SUCCESS;
}

/* 模拟从UART读取MS901M数据 */
static int32_t ReadMs901mRawData(struct SensorCfgData *data, struct AccelData *rawData, uint64_t *timestamp)
{
    uint8_t buffer[MS901M_GYRO_PAYLOAD_SIZE];
    OsalTimespec time;

    (void)memset_s(&time, sizeof(time), 0, sizeof(time));
    (void)memset_s(buffer, sizeof(buffer), 0, sizeof(buffer));

    CHECK_NULL_PTR_RETURN_VALUE(data, HDF_ERR_INVALID_PARAM);

    if (OsalGetTime(&time) != HDF_SUCCESS) {
        HDF_LOGE("%s: Get time failed", __func__);
        return HDF_FAILURE;
    }
    *timestamp = time.sec * SENSOR_SECOND_CONVERT_NANOSECOND + time.usec * SENSOR_CONVERT_UNIT;

    // 这里应该实现真正的UART读取逻辑
    // 为了简化，这里使用模拟数据
    // 实际应用中需要：
    // 1. 打开UART设备
    // 2. 读取数据帧
    // 3. 解析帧头和帧ID
    // 4. 提取加速度数据
    
    // 模拟读取到的加速度数据（小端格式）
    buffer[0] = 0x00; buffer[1] = 0x10;  // X轴加速度
    buffer[2] = 0x00; buffer[3] = 0x20;  // Y轴加速度  
    buffer[4] = 0x00; buffer[5] = 0x30;  // Z轴加速度

    return Ms901mDecodeAccelData(buffer, rawData);
}

/* 读取MS901M加速度传感器数据 */
static int32_t ReadMs901mData(struct SensorCfgData *cfg, struct SensorReportEvent *event)
{
    int32_t ret;
    struct AccelData rawData = { 0, 0, 0 };
    static int32_t tmp[ACCEL_AXIS_NUM];

    CHECK_NULL_PTR_RETURN_VALUE(cfg, HDF_ERR_INVALID_PARAM);
    CHECK_NULL_PTR_RETURN_VALUE(event, HDF_ERR_INVALID_PARAM);

    ret = ReadMs901mRawData(cfg, &rawData, &event->timestamp);
    if (ret != HDF_SUCCESS) {
        HDF_LOGE("%s: MS901M read raw data failed", __func__);
        return HDF_FAILURE;
    }

    event->sensorId = SENSOR_TAG_ACCELEROMETER;
    event->option = 0;
    event->mode = SENSOR_WORK_MODE_REALTIME;

    // 应用灵敏度转换
    rawData.x = rawData.x * MS901M_ACC_SENSITIVITY_2G;
    rawData.y = rawData.y * MS901M_ACC_SENSITIVITY_2G;
    rawData.z = rawData.z * MS901M_ACC_SENSITIVITY_2G;

    tmp[ACCEL_X_AXIS] = (rawData.x * SENSOR_CONVERT_UNIT) / SENSOR_CONVERT_UNIT;
    tmp[ACCEL_Y_AXIS] = (rawData.y * SENSOR_CONVERT_UNIT) / SENSOR_CONVERT_UNIT;
    tmp[ACCEL_Z_AXIS] = (rawData.z * SENSOR_CONVERT_UNIT) / SENSOR_CONVERT_UNIT;

    ret = SensorRawDataToRemapData(cfg->direction, tmp, sizeof(tmp) / sizeof(tmp[0]));
    if (ret != HDF_SUCCESS) {
        HDF_LOGE("%s: MS901M convert raw data failed", __func__);
        return HDF_FAILURE;
    }

    event->dataLen = sizeof(tmp);
    event->data = (uint8_t *)&tmp;

    return ret;
}

/* 初始化MS901M传感器 */
static int32_t InitMs901m(struct SensorCfgData *data)
{
    CHECK_NULL_PTR_RETURN_VALUE(data, HDF_ERR_INVALID_PARAM);
    
    // 这里可以添加UART初始化代码
    // 实际应用中需要：
    // 1. 配置UART参数（波特率、数据位等）
    // 2. 打开UART设备
    // 3. 配置传感器工作模式
    
    HDF_LOGI("%s: MS901M accel sensor init success", __func__);
    return HDF_SUCCESS;
}

/* 设备IO分发函数 */
static int32_t DispatchMs901m(struct HdfDeviceIoClient *client,
    int cmd, struct HdfSBuf *data, struct HdfSBuf *reply)
{
    (void)client;
    (void)cmd;
    (void)data;
    (void)reply;

    return HDF_SUCCESS;
}

/* 绑定驱动 */
static int32_t Ms901mBindDriver(struct HdfDeviceObject *device)
{
    CHECK_NULL_PTR_RETURN_VALUE(device, HDF_ERR_INVALID_PARAM);

    struct Ms901mDrvData *drvData = (struct Ms901mDrvData *)OsalMemCalloc(sizeof(*drvData));
    if (drvData == NULL) {
        HDF_LOGE("%s: Malloc MS901M drv data fail", __func__);
        return HDF_ERR_MALLOC_FAIL;
    }

    drvData->ioService.Dispatch = DispatchMs901m;
    drvData->device = device;
    device->service = &drvData->ioService;
    g_ms901mDrvData = drvData;

    return HDF_SUCCESS;
}

/* 初始化驱动 */
static int32_t Ms901mInitDriver(struct HdfDeviceObject *device)
{
    int32_t ret;
    struct AccelOpsCall ops;

    CHECK_NULL_PTR_RETURN_VALUE(device, HDF_ERR_INVALID_PARAM);
    struct Ms901mDrvData *drvData = (struct Ms901mDrvData *)device->service;
    CHECK_NULL_PTR_RETURN_VALUE(drvData, HDF_ERR_INVALID_PARAM);

    drvData->sensorCfg = AccelCreateCfgData(device->property);
    if (drvData->sensorCfg == NULL || drvData->sensorCfg->root == NULL) {
        HDF_LOGD("%s: Creating ms901m cfg failed because detection failed", __func__);
        return HDF_ERR_NOT_SUPPORT;
    }

    ops.Init = NULL;
    ops.ReadData = ReadMs901mData;
    ret = AccelRegisterChipOps(&ops);
    if (ret != HDF_SUCCESS) {
        HDF_LOGE("%s: Register MS901M accel failed", __func__);
        return HDF_FAILURE;
    }

    ret = InitMs901m(drvData->sensorCfg);
    if (ret != HDF_SUCCESS) {
        HDF_LOGE("%s: Init MS901M accel failed", __func__);
        return HDF_FAILURE;
    }

    return HDF_SUCCESS;
}

/* 释放驱动 */
static void Ms901mReleaseDriver(struct HdfDeviceObject *device)
{
    CHECK_NULL_PTR_RETURN(device);

    struct Ms901mDrvData *drvData = (struct Ms901mDrvData *)device->service;
    CHECK_NULL_PTR_RETURN(drvData);

    if (drvData->sensorCfg != NULL) {
        AccelReleaseCfgData(drvData->sensorCfg);
        drvData->sensorCfg = NULL;
    }
    OsalMemFree(drvData);
}

/* HDF驱动入口结构 */
struct HdfDriverEntry g_accelMs901mDevEntry = {
    .moduleVersion = 1,
    .moduleName = "HDF_SENSOR_ACCEL_MS901M",
    .Bind = Ms901mBindDriver,
    .Init = Ms901mInitDriver,
    .Release = Ms901mReleaseDriver,
};

HDF_INIT(g_accelMs901mDevEntry);
