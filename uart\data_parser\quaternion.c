#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <string.h>
#include <malloc.h>
#include "../common/common.h"
#include "quaternion.h"

#define QUATERNION_PAYLOAD_SIZE 8


// 将QuaternionData转换为JSON字符串（简化版，不包括实际的JSON库使用）  
char *quaternion_data_to_json(quaternion_data *data) {
    // 假设最大JSON字符串长度为256  
    char *jsonString = (char *) malloc(256 * sizeof(char));
    if (jsonString == NULL) return NULL;

    snprintf(jsonString, 256,
             "{\"source\":\"quaternion\",\"data\":{\"q0\":%.6f,\"q1\":%.6f,\"q2\":%.6f,\"q3\":%.6f}}",
             data->q0, data->q1, data->q2, data->q3);

    return jsonString;
}

quaternion_data *decode_quaternion(uint8_t *payload) {
    //printf("[%s, %d]\n", __FUNCTION__, __LINE__);

    if (payload == NULL) {
        printf("[%s, %d]\n", __FUNCTION__, __LINE__);
        return NULL;
    }

    quaternion_data *data = (quaternion_data *) malloc(sizeof(quaternion_data));
    if (data == NULL) return NULL;

    uint8_t q0L = payload[0];
    uint8_t q0H = payload[1];
    uint8_t q1L = payload[2];
    uint8_t q1H = payload[3];
    uint8_t q2L = payload[4];
    uint8_t q2H = payload[5];
    uint8_t q3L = payload[6];
    uint8_t q3H = payload[7];

    int16_t q0_raw = (int16_t)((uint16_t) q0H << 8 | (uint16_t) q0L);
    int16_t q1_raw = (int16_t)((uint16_t) q1H << 8 | (uint16_t) q1L);
    int16_t q2_raw = (int16_t)((uint16_t) q2H << 8 | (uint16_t) q2L);
    int16_t q3_raw = (int16_t)((uint16_t) q3H << 8 | (uint16_t) q3L);

    data->q0 = (double) q0_raw / 32768.0;
    data->q1 = (double) q1_raw / 32768.0;
    data->q2 = (double) q2_raw / 32768.0;
    data->q3 = (double) q3_raw / 32768.0;

    return data;
}

int process_quaternion_data(uint8_t *payload) {
    //printf("[%s, %d] payload size = %d \n", __FUNCTION__, __LINE__, malloc_usable_size(payload));
    if (payload == NULL) {
        printf("Payload is NULL\n");
    }
    //print_buf_with_hex(payload, QUATERNION_PAYLOAD_SIZE);

    quaternion_data *data = decode_quaternion(payload);
    if (data != NULL) {
        char *json = quaternion_data_to_json(data);
        if (json != NULL) {
            printf("JSON: %s\n", json);
            free(json);
        }
        free(data);
    }

    return 0;
}
