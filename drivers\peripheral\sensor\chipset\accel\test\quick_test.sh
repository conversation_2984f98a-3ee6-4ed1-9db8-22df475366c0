#!/bin/bash

# MS901M加速度传感器快速测试脚本
# 用于快速验证驱动是否正常工作

set -e

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${GREEN}MS901M加速度传感器快速测试${NC}"
echo "=================================="

# 检查是否为root
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}错误: 请以root用户运行${NC}"
    echo "使用: sudo $0"
    exit 1
fi

# 1. 检查驱动模块
echo -n "检查驱动模块... "
if lsmod | grep -q "ms901m"; then
    echo -e "${GREEN}✓ 已加载${NC}"
else
    echo -e "${YELLOW}未加载，尝试加载...${NC}"
    if modprobe ms901m 2>/dev/null; then
        echo -e "${GREEN}✓ 加载成功${NC}"
    else
        echo -e "${RED}✗ 加载失败${NC}"
        exit 1
    fi
fi

# 2. 检查设备节点
echo -n "检查设备节点... "
if [ -e "/dev/sensor_accel_ms901m" ]; then
    echo -e "${GREEN}✓ 存在${NC}"
    chmod 666 /dev/sensor_accel_ms901m 2>/dev/null || true
else
    echo -e "${RED}✗ 不存在${NC}"
    exit 1
fi

# 3. 编译测试程序
echo -n "编译测试程序... "
if make -s clean && make -s; then
    echo -e "${GREEN}✓ 成功${NC}"
else
    echo -e "${RED}✗ 失败${NC}"
    exit 1
fi

# 4. 运行基本测试
echo -n "运行基本功能测试... "
if timeout 10 ./ms901m_accel_test -b >/dev/null 2>&1; then
    echo -e "${GREEN}✓ 通过${NC}"
else
    echo -e "${RED}✗ 失败${NC}"
    echo "运行详细测试查看错误信息:"
    echo "./ms901m_accel_test -b"
    exit 1
fi

# 5. 快速数据采集测试
echo -n "运行数据采集测试... "
if timeout 5 python3 ms901m_data_monitor.py -t 3 >/dev/null 2>&1; then
    echo -e "${GREEN}✓ 通过${NC}"
else
    echo -e "${YELLOW}⚠ Python测试失败，尝试C程序测试${NC}"
    if timeout 10 ./ms901m_accel_test -c >/dev/null 2>&1; then
        echo -e "${GREEN}✓ C程序测试通过${NC}"
    else
        echo -e "${RED}✗ 数据采集失败${NC}"
        exit 1
    fi
fi

echo ""
echo -e "${GREEN}🎉 所有快速测试通过！${NC}"
echo ""
echo "下一步可以运行:"
echo "  ./ms901m_accel_test           # 完整功能测试"
echo "  python3 ms901m_data_monitor.py -p  # 实时数据可视化"
echo "  sudo ./test_ms901m_driver.sh  # 完整系统测试"
echo ""
