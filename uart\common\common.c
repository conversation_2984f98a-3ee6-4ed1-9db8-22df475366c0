
#include <stdio.h>
#include <stdint.h>


int print_buf_with_hex(uint8_t *buf, int len)
{

    int i;  
    for (i = 0; i < len; i++) {  
        // 使用%02x格式说明符，以两位16进制格式输出每个字节  
        // %02X也可以，它会输出大写字母  
        printf("%02x ", buf[i]);  
        // 每输出32个字节后换行（可选）  
        if ((i + 1) % 32 == 0) {  
            printf("\n");
        }  
    }
    printf("\n"); // 确保最后有一个换行符（如果需要）
    return 0;
}

