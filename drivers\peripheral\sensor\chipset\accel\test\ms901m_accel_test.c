/*
 * Copyright (c) 2024 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <signal.h>
#include <time.h>
#include <fcntl.h>
#include <errno.h>
#include <math.h>
#include "hilog/log.h"
#include "securec.h"

/* OpenHarmony日志标签 */
#undef LOG_DOMAIN
#undef LOG_TAG
#define LOG_DOMAIN 0xD002D00
#define LOG_TAG "MS901M_TEST"

/* 传感器相关定义 */
#define SENSOR_TYPE_ACCELEROMETER    1
#define SENSOR_DEVICE_PATH          "/dev/sensor_accel_ms901m"
#define SENSOR_DATA_SIZE            12  /* 3轴 * 4字节 */

/* 测试配置 */
#define TEST_DURATION_SECONDS       10
#define TEST_SAMPLE_INTERVAL_MS     100
#define MAX_SAMPLES                 1000

/* 传感器数据结构 */
struct SensorData {
    int32_t x;          /* X轴加速度 (mg) */
    int32_t y;          /* Y轴加速度 (mg) */
    int32_t z;          /* Z轴加速度 (mg) */
    uint64_t timestamp; /* 时间戳 (ns) */
};

/* 测试统计信息 */
struct TestStats {
    int32_t totalSamples;
    int32_t validSamples;
    int32_t errorSamples;
    double avgX, avgY, avgZ;
    int32_t minX, maxX;
    int32_t minY, maxY;
    int32_t minZ, maxZ;
    uint64_t startTime;
    uint64_t endTime;
};

static volatile int g_testRunning = 1;
static struct TestStats g_stats;

/* 信号处理函数 */
void SignalHandler(int sig)
{
    HILOG_INFO(LOG_CORE, "收到信号 %{public}d，停止测试...", sig);
    printf("\n收到信号 %d，停止测试...\n", sig);
    g_testRunning = 0;
}

/* 获取当前时间戳 (纳秒) */
uint64_t GetCurrentTimeNs(void)
{
    struct timespec ts;
    clock_gettime(CLOCK_MONOTONIC, &ts);
    return (uint64_t)ts.tv_sec * 1000000000ULL + (uint64_t)ts.tv_nsec;
}

/* 初始化测试统计 */
void InitTestStats(struct TestStats *stats)
{
    (void)memset_s(stats, sizeof(struct TestStats), 0, sizeof(struct TestStats));
    stats->minX = stats->minY = stats->minZ = INT32_MAX;
    stats->maxX = stats->maxY = stats->maxZ = INT32_MIN;
    stats->startTime = GetCurrentTimeNs();
}

/* 更新测试统计 */
void UpdateTestStats(struct TestStats *stats, const struct SensorData *data)
{
    stats->totalSamples++;
    
    if (data->x == 0 && data->y == 0 && data->z == 0) {
        stats->errorSamples++;
        return;
    }
    
    stats->validSamples++;
    
    /* 更新平均值 */
    stats->avgX = (stats->avgX * (stats->validSamples - 1) + data->x) / stats->validSamples;
    stats->avgY = (stats->avgY * (stats->validSamples - 1) + data->y) / stats->validSamples;
    stats->avgZ = (stats->avgZ * (stats->validSamples - 1) + data->z) / stats->validSamples;
    
    /* 更新最值 */
    if (data->x < stats->minX) stats->minX = data->x;
    if (data->x > stats->maxX) stats->maxX = data->x;
    if (data->y < stats->minY) stats->minY = data->y;
    if (data->y > stats->maxY) stats->maxY = data->y;
    if (data->z < stats->minZ) stats->minZ = data->z;
    if (data->z > stats->maxZ) stats->maxZ = data->z;
}

/* 打印测试统计 */
void PrintTestStats(const struct TestStats *stats)
{
    double testDuration = (stats->endTime - stats->startTime) / 1000000000.0;
    double sampleRate = stats->totalSamples / testDuration;
    
    printf("\n=== MS901M加速度传感器测试统计 ===\n");
    printf("测试时长: %.2f 秒\n", testDuration);
    printf("总采样数: %d\n", stats->totalSamples);
    printf("有效采样: %d\n", stats->validSamples);
    printf("错误采样: %d\n", stats->errorSamples);
    printf("采样率: %.2f Hz\n", sampleRate);
    printf("成功率: %.2f%%\n", (double)stats->validSamples / stats->totalSamples * 100);
    
    if (stats->validSamples > 0) {
        printf("\n--- 加速度数据统计 (mg) ---\n");
        printf("X轴: 平均=%.2f, 范围=[%d, %d]\n", stats->avgX, stats->minX, stats->maxX);
        printf("Y轴: 平均=%.2f, 范围=[%d, %d]\n", stats->avgY, stats->minY, stats->maxY);
        printf("Z轴: 平均=%.2f, 范围=[%d, %d]\n", stats->avgZ, stats->minZ, stats->maxZ);
        
        /* 计算重力加速度模长 */
        double magnitude = sqrt(stats->avgX * stats->avgX + 
                               stats->avgY * stats->avgY + 
                               stats->avgZ * stats->avgZ);
        printf("重力加速度模长: %.2f mg (理论值: 1000mg)\n", magnitude);
    }
}

/* 读取传感器数据 */
int ReadSensorData(int fd, struct SensorData *data)
{
    int32_t rawData[3];
    ssize_t bytesRead;
    
    /* 读取原始数据 */
    bytesRead = read(fd, rawData, sizeof(rawData));
    if (bytesRead != sizeof(rawData)) {
        if (bytesRead < 0) {
            printf("读取传感器数据失败: %s\n", strerror(errno));
        } else {
            printf("读取数据长度不正确: %zd (期望: %zu)\n", bytesRead, sizeof(rawData));
        }
        return -1;
    }
    
    /* 转换数据格式 */
    data->x = rawData[0];
    data->y = rawData[1];
    data->z = rawData[2];
    data->timestamp = GetCurrentTimeNs();
    
    return 0;
}

/* 测试传感器基本功能 */
int TestSensorBasic(void)
{
    int fd;
    struct SensorData data;

    printf("=== 基本功能测试 ===\n");
    HILOG_INFO(LOG_CORE, "开始基本功能测试");

    /* 打开传感器设备 */
    fd = open(SENSOR_DEVICE_PATH, O_RDONLY);
    if (fd < 0) {
        printf("打开传感器设备失败: %s\n", strerror(errno));
        printf("请检查:\n");
        printf("1. 驱动是否已加载: lsmod | grep ms901m\n");
        printf("2. 设备节点是否存在: ls -l %s\n", SENSOR_DEVICE_PATH);
        printf("3. 权限是否正确: 需要读权限\n");
        HILOG_ERROR(LOG_CORE, "打开传感器设备失败: %{public}s", strerror(errno));
        return -1;
    }

    printf("成功打开传感器设备: %s\n", SENSOR_DEVICE_PATH);
    HILOG_INFO(LOG_CORE, "成功打开传感器设备");

    /* 读取一次数据测试 */
    if (ReadSensorData(fd, &data) == 0) {
        printf("成功读取传感器数据:\n");
        printf("  X轴: %d mg\n", data.x);
        printf("  Y轴: %d mg\n", data.y);
        printf("  Z轴: %d mg\n", data.z);
        printf("  时间戳: %llu ns\n", (unsigned long long)data.timestamp);
        HILOG_INFO(LOG_CORE, "读取传感器数据: X=%{public}d, Y=%{public}d, Z=%{public}d mg",
                   data.x, data.y, data.z);
    } else {
        printf("读取传感器数据失败\n");
        HILOG_ERROR(LOG_CORE, "读取传感器数据失败");
        close(fd);
        return -1;
    }

    close(fd);
    printf("基本功能测试通过\n\n");
    HILOG_INFO(LOG_CORE, "基本功能测试通过");
    return 0;
}

/* 连续数据采集测试 */
int TestSensorContinuous(void)
{
    int fd;
    struct SensorData data;
    int sampleCount = 0;
    
    printf("=== 连续数据采集测试 ===\n");
    printf("测试时长: %d 秒\n", TEST_DURATION_SECONDS);
    printf("采样间隔: %d ms\n", TEST_SAMPLE_INTERVAL_MS);
    printf("按 Ctrl+C 提前停止测试\n\n");
    
    /* 打开传感器设备 */
    fd = open(SENSOR_DEVICE_PATH, O_RDONLY);
    if (fd < 0) {
        printf("打开传感器设备失败: %s\n", strerror(errno));
        return -1;
    }
    
    /* 初始化统计信息 */
    InitTestStats(&g_stats);
    
    /* 连续采集数据 */
    while (g_testRunning && sampleCount < MAX_SAMPLES) {
        if (ReadSensorData(fd, &data) == 0) {
            UpdateTestStats(&g_stats, &data);
            
            /* 每10个样本打印一次进度 */
            if (sampleCount % 10 == 0) {
                printf("\r样本 %d: X=%d, Y=%d, Z=%d mg", 
                       sampleCount + 1, data.x, data.y, data.z);
                fflush(stdout);
            }
            
            sampleCount++;
        }
        
        /* 检查测试时长 */
        uint64_t currentTime = GetCurrentTimeNs();
        if ((currentTime - g_stats.startTime) / 1000000000ULL >= TEST_DURATION_SECONDS) {
            break;
        }
        
        /* 等待下次采样 */
        usleep(TEST_SAMPLE_INTERVAL_MS * 1000);
    }
    
    g_stats.endTime = GetCurrentTimeNs();
    close(fd);
    
    printf("\n\n连续采集测试完成\n");
    return 0;
}

/* 显示帮助信息 */
void ShowHelp(const char *progName)
{
    printf("MS901M加速度传感器测试程序\n");
    printf("用法: %s [选项]\n", progName);
    printf("选项:\n");
    printf("  -h, --help     显示帮助信息\n");
    printf("  -b, --basic    只运行基本功能测试\n");
    printf("  -c, --continuous 只运行连续采集测试\n");
    printf("  -a, --all      运行所有测试 (默认)\n");
    printf("\n");
    printf("示例:\n");
    printf("  %s              # 运行所有测试\n", progName);
    printf("  %s -b           # 只测试基本功能\n", progName);
    printf("  %s -c           # 只测试连续采集\n", progName);
}

int main(int argc, char *argv[])
{
    int runBasic = 1;
    int runContinuous = 1;
    
    /* 注册信号处理函数 */
    signal(SIGINT, SignalHandler);
    signal(SIGTERM, SignalHandler);
    
    /* 解析命令行参数 */
    if (argc > 1) {
        if (strcmp(argv[1], "-h") == 0 || strcmp(argv[1], "--help") == 0) {
            ShowHelp(argv[0]);
            return 0;
        } else if (strcmp(argv[1], "-b") == 0 || strcmp(argv[1], "--basic") == 0) {
            runContinuous = 0;
        } else if (strcmp(argv[1], "-c") == 0 || strcmp(argv[1], "--continuous") == 0) {
            runBasic = 0;
        } else if (strcmp(argv[1], "-a") == 0 || strcmp(argv[1], "--all") == 0) {
            /* 默认行为，运行所有测试 */
        } else {
            printf("未知选项: %s\n", argv[1]);
            ShowHelp(argv[0]);
            return 1;
        }
    }
    
    printf("MS901M加速度传感器驱动测试程序\n");
    printf("========================================\n\n");
    
    /* 运行基本功能测试 */
    if (runBasic) {
        if (TestSensorBasic() != 0) {
            printf("基本功能测试失败，退出\n");
            return 1;
        }
    }
    
    /* 运行连续采集测试 */
    if (runContinuous && g_testRunning) {
        if (TestSensorContinuous() == 0) {
            PrintTestStats(&g_stats);
        }
    }
    
    printf("\n测试完成\n");
    return 0;
}
