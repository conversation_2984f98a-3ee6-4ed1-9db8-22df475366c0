# MS901M加速度传感器驱动测试指南

## 概述

本目录包含了完整的MS901M加速度传感器驱动测试工具集，用于验证驱动功能、性能和稳定性。

## 测试工具

### 1. C语言测试程序 (`ms901m_accel_test.c`)

**功能**：
- 基本功能测试：验证设备打开、数据读取
- 连续采集测试：长时间数据采集和统计分析
- 性能测试：测试数据读取速度和稳定性

**编译**：
```bash
# 本地编译
make

# ARM64交叉编译
make arm64

# ARM32交叉编译  
make arm32
```

**使用**：
```bash
# 运行所有测试
./ms901m_accel_test

# 只运行基本功能测试
./ms901m_accel_test -b

# 只运行连续采集测试
./ms901m_accel_test -c

# 显示帮助
./ms901m_accel_test -h
```

### 2. Shell测试脚本 (`test_ms901m_driver.sh`)

**功能**：
- 自动化驱动环境检查
- 系统集成测试
- 日志分析和报告生成
- 性能基准测试

**使用**：
```bash
# 运行完整测试
sudo ./test_ms901m_driver.sh

# 只检查驱动状态
sudo ./test_ms901m_driver.sh -c

# 只运行功能测试
sudo ./test_ms901m_driver.sh -t

# 运行性能测试
sudo ./test_ms901m_driver.sh -p

# 生成详细报告
sudo ./test_ms901m_driver.sh -r
```

### 3. Python数据监控工具 (`ms901m_data_monitor.py`)

**功能**：
- 实时数据监控
- 数据可视化图表
- 统计分析
- 数据导出

**依赖安装**：
```bash
# 安装matplotlib (可选，用于图表显示)
pip3 install matplotlib
```

**使用**：
```bash
# 控制台监控模式
python3 ms901m_data_monitor.py

# 实时图表模式
python3 ms901m_data_monitor.py -p

# 监控10秒并保存数据
python3 ms901m_data_monitor.py -t 10 -s data.csv

# 指定设备路径
python3 ms901m_data_monitor.py -d /dev/sensor_accel_ms901m

# 显示帮助
python3 ms901m_data_monitor.py -h
```

## 测试流程

### 1. 环境准备

**检查驱动状态**：
```bash
# 检查模块是否加载
lsmod | grep ms901m

# 检查设备节点
ls -l /dev/sensor_accel_ms901m

# 检查传感器列表
ls -l /sys/class/sensor/
```

**加载驱动**（如果未加载）：
```bash
# 加载模块
sudo modprobe ms901m

# 或者手动加载
sudo insmod /path/to/ms901m.ko
```

### 2. 快速测试

**一键测试**：
```bash
cd drivers/peripheral/sensor/chipset/accel/test
sudo ./test_ms901m_driver.sh
```

**手动测试**：
```bash
# 编译测试程序
make

# 运行基本测试
./ms901m_accel_test -b

# 运行连续测试
./ms901m_accel_test -c
```

### 3. 详细测试

**功能测试**：
```bash
# 测试设备打开/关闭
./ms901m_accel_test -b

# 测试数据读取
python3 ms901m_data_monitor.py -t 5

# 测试长时间稳定性
./ms901m_accel_test -c
```

**性能测试**：
```bash
# 测试读取速度
sudo ./test_ms901m_driver.sh -p

# 测试采样率
python3 ms901m_data_monitor.py -t 10 -i 0.01
```

**数据质量测试**：
```bash
# 静态测试（传感器静止）
python3 ms901m_data_monitor.py -t 30 -s static_test.csv

# 动态测试（移动传感器）
python3 ms901m_data_monitor.py -p
```

## 测试结果分析

### 1. 正常工作指标

**基本功能**：
- ✅ 设备节点存在且可读
- ✅ 能够成功读取数据
- ✅ 数据格式正确（3个int32值）

**数据质量**：
- ✅ 静止时Z轴接近1000mg（重力加速度）
- ✅ 数据变化合理，无异常跳变
- ✅ 采样率稳定（通常20-100Hz）

**性能指标**：
- ✅ 读取延迟 < 10ms
- ✅ 数据更新频率 > 20Hz
- ✅ 长时间运行无内存泄漏

### 2. 常见问题诊断

**设备节点不存在**：
```bash
# 检查驱动加载
lsmod | grep ms901m

# 检查内核日志
dmesg | grep ms901m

# 检查设备树配置
cat /proc/device-tree/sensor_host/*/compatible
```

**数据读取失败**：
```bash
# 检查权限
ls -l /dev/sensor_accel_ms901m

# 检查UART设备
ls -l /dev/ttyS5

# 查看系统日志
hilog | grep sensor
```

**数据异常**：
```bash
# 检查数据格式
hexdump -C /dev/sensor_accel_ms901m | head

# 检查UART通信
cat /dev/ttyS5 | hexdump -C
```

### 3. 性能基准

**正常性能指标**：
- 数据读取延迟：< 5ms
- 采样率：20-100Hz
- CPU占用率：< 1%
- 内存占用：< 1MB

**数据质量指标**：
- 静止时噪声：< 10mg RMS
- 重力加速度精度：±50mg
- 数据更新一致性：> 95%

## 故障排除

### 1. 驱动问题

**症状**：设备节点不存在
**解决**：
```bash
# 检查内核配置
grep CONFIG_DRIVERS_HDF_SENSOR_ACCEL_MS901M /boot/config-$(uname -r)

# 重新编译和加载驱动
make clean && make
sudo insmod ms901m.ko
```

**症状**：模块加载失败
**解决**：
```bash
# 检查依赖
modinfo ms901m.ko

# 检查内核版本兼容性
uname -r
```

### 2. 硬件问题

**症状**：读取数据全为0
**解决**：
```bash
# 检查UART连接
ls -l /dev/ttyS*

# 测试UART通信
echo "test" > /dev/ttyS5
cat /dev/ttyS5
```

**症状**：数据异常跳变
**解决**：
- 检查UART波特率配置
- 检查硬件连接稳定性
- 检查电源供应

### 3. 系统集成问题

**症状**：权限拒绝
**解决**：
```bash
# 修改设备权限
sudo chmod 666 /dev/sensor_accel_ms901m

# 添加用户到相应组
sudo usermod -a -G dialout $USER
```

## 自动化测试

### 1. 持续集成测试

创建自动化测试脚本：
```bash
#!/bin/bash
# ci_test.sh - 持续集成测试脚本

set -e

echo "开始MS901M驱动CI测试..."

# 编译测试
make clean && make

# 基本功能测试
timeout 30 ./ms901m_accel_test -b

# 短时间稳定性测试
timeout 60 ./ms901m_accel_test -c

echo "CI测试通过"
```

### 2. 回归测试

定期运行完整测试：
```bash
# 每日回归测试
0 2 * * * /path/to/test_ms901m_driver.sh -r > /var/log/ms901m_test.log 2>&1
```

## 总结

这套测试工具提供了全面的MS901M加速度传感器驱动验证能力：

1. **功能验证**：确保基本读写功能正常
2. **性能测试**：验证数据质量和系统性能
3. **稳定性测试**：长时间运行验证
4. **可视化分析**：实时数据监控和分析
5. **自动化测试**：支持CI/CD集成

通过这些测试工具，可以快速定位问题、验证修复效果，确保驱动的可靠性和稳定性。
