/*
 * Copyright (c) 2024 Huawei Device Co., Ltd.
 *
 * HDF is dual licensed: you can use it either under the terms of
 * the GPL, or the BSD license, at your option.
 * See the LICENSE file in the root of this repository for complete details.
 */

#include "sensor_accelerometer_driver.h"
#include "hdf_base.h"
#include "hdf_log.h"
#include "osal_mem.h"
#include "sensor_config_parser.h"

#define HDF_LOG_TAG sensor_accelerometer_driver

/* 全局变量 */
static struct AccelerometerDrvData *g_accelerometerDrvData = NULL;
static struct AccelerometerOpsCall g_accelerometerOps = {0};

/* 工作队列回调函数 */
static void AccelerometerWorkFunc(struct HdfWork *work)
{
    struct AccelerometerDrvData *drvData = NULL;
    if (work == NULL) {
        HDF_LOGE("%s: work is null", __func__);
        return;
    }

    drvData = CONTAINER_OF(work, struct AccelerometerDrvData, accelerometerWork);
    if (drvData == NULL || drvData->accelerometerCfg == NULL) {
        HDF_LOGE("%s: drvData or accelerometerCfg is null", __func__);
        return;
    }

    if (drvData->ops.ReadData != NULL) {
        drvData->ops.ReadData(drvData->accelerometerCfg);
    }
}

/* 定时器回调函数 */
static void AccelerometerTimerFunc(uintptr_t arg)
{
    struct AccelerometerDrvData *drvData = (struct AccelerometerDrvData *)arg;
    if (drvData == NULL) {
        HDF_LOGE("%s: drvData is null", __func__);
        return;
    }

    if (drvData->enable) {
        HdfWorkSubmit(&drvData->accelerometerWork);
    }
}

/* 初始化加速度传感器 */
static int32_t AccelerometerInit(struct HdfDeviceObject *device)
{
    struct AccelerometerDrvData *drvData = NULL;
    if (device == NULL) {
        HDF_LOGE("%s: device is null", __func__);
        return HDF_ERR_INVALID_PARAM;
    }

    drvData = (struct AccelerometerDrvData *)OsalMemCalloc(sizeof(*drvData));
    if (drvData == NULL) {
        HDF_LOGE("%s: malloc drvData failed", __func__);
        return HDF_ERR_MALLOC_FAIL;
    }

    drvData->device = device;
    drvData->enable = false;
    drvData->interval = ACCELEROMETER_DEFAULT_SAMPLING_1000_MS;

    /* 创建工作队列 */
    if (HdfWorkQueueInit(&drvData->accelerometerWorkQueue, "accelerometer_work_queue") != HDF_SUCCESS) {
        HDF_LOGE("%s: init work queue failed", __func__);
        OsalMemFree(drvData);
        return HDF_FAILURE;
    }

    /* 初始化工作项 */
    HdfWorkInit(&drvData->accelerometerWork, AccelerometerWorkFunc, NULL);

    /* 初始化定时器 */
    if (OsalTimerCreate(&drvData->accelerometerTimer, AccelerometerTimerFunc, (uintptr_t)drvData) != HDF_SUCCESS) {
        HDF_LOGE("%s: create timer failed", __func__);
        HdfWorkQueueDestroy(&drvData->accelerometerWorkQueue);
        OsalMemFree(drvData);
        return HDF_FAILURE;
    }

    g_accelerometerDrvData = drvData;
    return HDF_SUCCESS;
}

/* 释放加速度传感器资源 */
static void AccelerometerRelease(struct HdfDeviceObject *device)
{
    struct AccelerometerDrvData *drvData = g_accelerometerDrvData;
    if (drvData == NULL) {
        return;
    }

    if (drvData->enable) {
        drvData->enable = false;
        OsalTimerDelete(drvData->accelerometerTimer);
    }

    HdfWorkQueueDestroy(&drvData->accelerometerWorkQueue);
    if (drvData->accelerometerCfg != NULL) {
        AccelerometerReleaseCfgData(drvData->accelerometerCfg);
    }

    OsalMemFree(drvData);
    g_accelerometerDrvData = NULL;
}

/* 注册芯片操作函数 */
int32_t AccelerometerRegisterChipOps(const struct AccelerometerOpsCall *ops)
{
    if (ops == NULL) {
        HDF_LOGE("%s: ops is null", __func__);
        return HDF_ERR_INVALID_PARAM;
    }

    g_accelerometerOps = *ops;
    return HDF_SUCCESS;
}

/* 创建配置数据 */
struct SensorCfgData *AccelerometerCreateCfgData(const struct DeviceResourceNode *node)
{
    struct SensorCfgData *cfgData = NULL;
    if (node == NULL) {
        HDF_LOGE("%s: node is null", __func__);
        return NULL;
    }

    cfgData = (struct SensorCfgData *)OsalMemCalloc(sizeof(*cfgData));
    if (cfgData == NULL) {
        HDF_LOGE("%s: malloc cfgData failed", __func__);
        return NULL;
    }

    /* 解析设备树配置 */
    if (ParseSensorCfgData(node, cfgData) != HDF_SUCCESS) {
        HDF_LOGE("%s: parse sensor config failed", __func__);
        OsalMemFree(cfgData);
        return NULL;
    }

    return cfgData;
}

/* 释放配置数据 */
void AccelerometerReleaseCfgData(struct SensorCfgData *accelerometerCfg)
{
    if (accelerometerCfg == NULL) {
        return;
    }

    if (accelerometerCfg->priv != NULL) {
        OsalMemFree(accelerometerCfg->priv);
    }
    OsalMemFree(accelerometerCfg);
}

/* 设备驱动入口结构 */
struct HdfDriverEntry g_accelerometerDriverEntry = {
    .moduleVersion = 1,
    .moduleName = "HDF_SENSOR_ACCELEROMETER",
    .Bind = NULL,
    .Init = AccelerometerInit,
    .Release = AccelerometerRelease,
};

/* 驱动入口注册 */
HDF_INIT(g_accelerometerDriverEntry); 