# Copyright (c) 2021-2022 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
#

import("//build/ohos.gni")

print("demos: compile uart_test")

ohos_executable("uart_test") {

    sources = [
        "main.c",
        "serial_port/serial_port.c",
        "data_parser/euler.c",
        "data_parser/gyro.c",
        "data_parser/magnetic.c",
        "data_parser/portstat.c",
        "data_parser/pressure.c",
        "data_parser/quaternion.c",
        "common/common.c",
    ]
    include_dirs = [
        "//base/hiviewdfx/hilog/interfaces/native/innerkits/include",
        "//third_party/bounds_checking_function/include",
        "//third_party/cJSON",
    ]
  deps = [
    "//third_party/cJSON:cjson",
  ]
  external_deps = [
    "c_utils:utils",
    "hilog:libhilog",
  ]

    cflags = [
        "-Wall",
        "-Wextra",
        "-Werror",
        "-Wno-format",
        "-Wno-format-extra-args",
    ]

    part_name = "demos"
    install_enable = true
}
