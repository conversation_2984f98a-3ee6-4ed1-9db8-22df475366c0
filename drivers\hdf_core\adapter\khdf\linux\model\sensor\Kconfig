# Copyright (c) 2021 Huawei Device Co., Ltd.
#
# This software is licensed under the terms of the GNU General Public
# License version 2, as published by the Free Software Foundation, and
# may be copied, distributed, and modified under those terms.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
# GNU General Public License for more details.

config DRIVERS_HDF_SENSOR
    bool "Enable HDF sensor driver"
    default n
    depends on DRIVERS_HDF
    help
      Answer Y to enable HDF sensor driver.

config DRIVERS_HDF_SENSOR_ACCEL
    bool "Enable HDF accel sensor driver"
    default n
    depends on DRIVERS_HDF_SENSOR
    help
      Answer Y to enable HDF accel sensor driver.
config DRIVERS_HDF_SENSOR_ACCEL_BMI160
    bool "Enable HDF accel bmi160 sensor driver"
    default n
    depends on DRIVERS_HDF_SENSOR_ACCEL
    help
      Answer Y to enable HDF accel bmi160 sensor driver.
config DRIVERS_HDF_SENSOR_ACCEL_MXC6655XA
    bool "Enable HDF accel mxc6655xa sensor driver"
    default n
    depends on DRIVERS_HDF_SENSOR_ACCEL
    help
      Answer Y to enable HDF accel mxc6655xa sensor driver.
config DRIVERS_HDF_SENSOR_ACCEL_MS901M
    bool "Enable HDF accel ms901m sensor driver"
    default n
    depends on DRIVERS_HDF_SENSOR_ACCEL
    help
      Answer Y to enable HDF accel ms901m sensor driver.
config DRIVERS_HDF_SENSOR_PPG
    bool "Enable HDF ppg sensor driver"
    default n
    depends on DRIVERS_HDF_SENSOR
    help
      Answer Y to enable HDF ppg sensor driver.
config DRIVERS_HDF_SENSOR_PPG_CS1262
    bool "Enable HDF ppg ppg cs1262 sensor driver"
    default n
    depends on DRIVERS_HDF_SENSOR_PPG
    help
      Answer Y to enable HDF ppg ppg cs1262 sensor driver.
config DRIVERS_HDF_SENSOR_ALS
    bool "Enable HDF als sensor driver"
    default n
    depends on DRIVERS_HDF_SENSOR
    help
      Answer Y to enable HDF als sensor driver.
config DRIVERS_HDF_SENSOR_ALS_BH1745
    bool "Enable HDF als bh1745 sensor driver"
    default n
    depends on DRIVERS_HDF_SENSOR_ALS
    help
      Answer Y to enable HDF als bh1745 sensor driver.
config DRIVERS_HDF_SENSOR_ALS_BH1750
    bool "Enable HDF als bh1750 sensor driver"
    default n
    depends on DRIVERS_HDF_SENSOR_ALS
    help
      Answer Y to enable HDF als bh1750 sensor driver.
config DRIVERS_HDF_SENSOR_GRAVITY
    bool "Enable HDF gravity sensor driver"
    default n
    depends on DRIVERS_HDF_SENSOR_ACCEL
    help
      Answer Y to enable HDF gravity sensor driver.
config DRIVERS_HDF_SENSOR_GYRO
    bool "Enable HDF gyro sensor driver"
    default n
    depends on DRIVERS_HDF_SENSOR
    help
      Answer Y to enable HDF gyro sensor driver.
config DRIVERS_HDF_SENSOR_GYRO_BMI160
    bool "Enable HDF gyro bmi160 sensor driver"
    default n
    depends on DRIVERS_HDF_SENSOR_GYRO
    help
      Answer Y to enable HDF gyro bmi160 sensor driver.
config DRIVERS_HDF_SENSOR_PEDOMETER
    bool "Enable HDF pedometer sensor driver"
    default n
    depends on DRIVERS_HDF_SENSOR
    help
      Answer Y to enable HDF pedometer sensor driver.
config DRIVERS_HDF_SENSOR_PEDOMETER_BMI160
    bool "Enable HDF pedometer bmi160 sensor driver"
    default n
    depends on DRIVERS_HDF_SENSOR_PEDOMETER
    help
      Answer Y to enable HDF pedometer bmi160 sensor driver.
config DRIVERS_HDF_SENSOR_BAROMETER
    bool "Enable HDF barometer sensor driver"
    default n
    depends on DRIVERS_HDF_SENSOR
    help
      Answer Y to enable HDF barometer sensor driver.
config DRIVERS_HDF_SENSOR_BAROMETER_BMP180
    bool "Enable HDF barometer bmp180 sensor driver"
    default n
    depends on DRIVERS_HDF_SENSOR_BAROMETER
    help
      Answer Y to enable HDF barometer bmp180 sensor driver.
config DRIVERS_HDF_SENSOR_HALL
    bool "Enable HDF hall sensor driver"
    default n
    depends on DRIVERS_HDF_SENSOR
    help
      Answer Y to enable HDF hall sensor driver.
config DRIVERS_HDF_SENSOR_HALL_AK8789
    bool "Enable HDF hall ak8789 sensor driver"
    default n
    depends on DRIVERS_HDF_SENSOR_HALL
    help
      Answer Y to enable HDF hall ak8789 sensor driver.
config DRIVERS_HDF_SENSOR_HUMIDITY
    bool "Enable HDF humidity sensor driver"
    default n
    depends on DRIVERS_HDF_SENSOR
    help
      Answer Y to enable HDF humidity sensor driver.
config DRIVERS_HDF_SENSOR_HUMIDITY_AHT20
    bool "Enable HDF humidity aht20 sensor driver"
    default n
    depends on DRIVERS_HDF_SENSOR_HUMIDITY
    help
      Answer Y to enable HDF humidity aht20 sensor driver.
config DRIVERS_HDF_SENSOR_HUMIDITY_SHT30
    bool "Enable HDF humidity sht30 sensor driver"
    default n
    depends on DRIVERS_HDF_SENSOR_HUMIDITY
    help
      Answer Y to enable HDF humidity sht30 sensor driver.
config DRIVERS_HDF_SENSOR_MAGNETIC
    bool "Enable HDF magnetic sensor driver"
    default n
    depends on DRIVERS_HDF_SENSOR
    help
      Answer Y to enable HDF magnetic sensor driver.
config DRIVERS_HDF_SENSOR_MAGNETIC_LSM303
    bool "Enable HDF magnetic lsm303 sensor driver"
    default n
    depends on DRIVERS_HDF_SENSOR_MAGNETIC
    help
      Answer Y to enable HDF magnetic lsm303 sensor driver.
config DRIVERS_HDF_SENSOR_PROXIMITY
    bool "Enable HDF proximity sensor driver"
    default n
    depends on DRIVERS_HDF_SENSOR
    help
      Answer Y to enable HDF proximity sensor driver.
config DRIVERS_HDF_SENSOR_PROXIMITY_APDS9960
    bool "Enable HDF proximity adps9960 sensor driver"
    default n
    depends on DRIVERS_HDF_SENSOR_PROXIMITY
    help
      Answer Y to enable HDF proximity apds9960 sensor driver.
config DRIVERS_HDF_SENSOR_TEMPERATURE
    bool "Enable HDF temperature sensor driver"
    default n
    depends on DRIVERS_HDF_SENSOR
    help
      Answer Y to enable HDF temperature sensor driver.
config DRIVERS_HDF_SENSOR_TEMPERATURE_AHT20
    bool "Enable HDF temperature aht20 sensor driver"
    default n
    depends on DRIVERS_HDF_SENSOR_TEMPERATURE
    help
      Answer Y to enable HDF temperature aht20 sensor driver.
config DRIVERS_HDF_SENSOR_TEMPERATURE_SHT30
    bool "Enable HDF temperature sht30 sensor driver"
    default n
    depends on DRIVERS_HDF_SENSOR_TEMPERATURE
    help
      Answer Y to enable HDF temperature sht30 sensor driver.