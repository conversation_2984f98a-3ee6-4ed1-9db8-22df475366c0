#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <stdlib.h>
#include <string.h>
#include <malloc.h>
#include "../common/common.h"
#include "pressure.h"

#define PRESSURE_PAYLOAD_SIZE 10



// 将PressureData转换为JSON字符串
char *pressure_data_to_json(pressure_data *data) {
    // 假设最大JSON字符串长度为256  
    char *jsonString = (char *) malloc(256 * sizeof(char));
    if (jsonString == NULL) return NULL;

    snprintf(jsonString, 256,
             "{\"source\":\"pressure\",\"data\":{\"pressure\":%d,\"altitude\":%d,\"temperature\":%.2f}}",
             data->pressure, data->altitude, data->temperature);

    return jsonString;
}

pressure_data *decode_pressure(const uint8_t *payload) {
    if (payload == NULL) {
        printf("[%s, %d]\n", __FUNCTION__, __LINE__);
        return NULL;
    }

    pressure_data *pressureData = (pressure_data *) malloc(sizeof(pressure_data));
    if (pressureData == NULL) return NULL;

    uint8_t p0 = payload[0];
    uint8_t p1 = payload[1];
    uint8_t p2 = payload[2];
    uint8_t p3 = payload[3];
    uint8_t a0 = payload[4];
    uint8_t a1 = payload[5];
    uint8_t a2 = payload[6];
    uint8_t a3 = payload[7];
    uint8_t tL = payload[8];
    uint8_t tH = payload[9];

    pressureData->pressure = p0 | (p1 << 8) | (p2 << 16) | (p3 << 24);
    pressureData->altitude = a0 | (a1 << 8) | (a2 << 16) | (a3 << 24);
    pressureData->temperature = ((int16_t)(tH << 8) | (int16_t) tL) / 100.0;

    return pressureData;
}


int process_press_data(uint8_t *payload) {
    //printf("[%s, %d] payload size = %d \n", __FUNCTION__, __LINE__, malloc_usable_size(payload));
    if (payload == NULL) {
        printf("Payload is NULL\n");
    }
    //print_buf_with_hex(payload, PRESSURE_PAYLOAD_SIZE);

    pressure_data *data = decode_pressure(payload);
    if (data != NULL) {
        char *json = pressure_data_to_json(data);
        if (json != NULL) {
            printf("JSON: %s\n", json);
            free(json);
        }
        free(data);
    }

    return 0;
}